{"name": "mcp-video-recognition", "version": "1.0.0", "description": "MCP server for Google Gemini image, audio, and video recognition", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc -w & node --watch dist/index.js", "debug": "tsc & npx @modelcontextprotocol/inspector node dist/index.js", "lint": "eslint src --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "gemini", "video", "audio", "image", "recognition"], "author": "", "license": "MIT", "dependencies": {"@google/genai": "^0.9.0", "@modelcontextprotocol/sdk": "^1.10.1", "express": "^5.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^22.14.1", "typescript": "^5.8.3"}}