
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (be careful with this in production!)
DROP TABLE IF EXISTS public.site_content CASCADE;
DROP TABLE IF EXISTS public.images CASCADE;
DROP TABLE IF EXISTS public.category_items CASCADE;
DROP TABLE IF EXISTS public.categories CASCADE;
DROP TABLE IF EXISTS public.navigation_items CASCADE;

-- Create categories table
CREATE TABLE public.categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    display_order INTEGER NOT NULL,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true
);

-- Create category_items table
CREATE TABLE public.category_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    title TEXT NOT NULL,
    description TEXT,
    image_path TEXT NOT NULL,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE
);

-- Create navigation_items table
CREATE TABLE public.navigation_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    title TEXT NOT NULL,
    path TEXT NOT NULL,
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true
);

-- Create images table
CREATE TABLE public.images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    url TEXT NOT NULL,
    thumbnail_url TEXT,
    title TEXT,
    description TEXT,
    magazine_title TEXT,
    image_role TEXT,
    is_published BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}'::jsonb,
    user_id UUID
);

-- Create site_content table
CREATE TABLE public.site_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc'::text, now()),
    page VARCHAR NOT NULL,
    section VARCHAR NOT NULL,
    title TEXT,
    subtitle TEXT,
    description TEXT,
    image_url TEXT,
    content JSONB DEFAULT '{}'::jsonb
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create thumbnail generation function
CREATE OR REPLACE FUNCTION public.generate_thumbnail_url()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.url LIKE '%/storage/v1/object/public/%' THEN
        NEW.thumbnail_url = regexp_replace(NEW.url, '/public/', '/public/thumbnails/');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON public.categories
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_category_items_updated_at
    BEFORE UPDATE ON public.category_items
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_navigation_items_updated_at
    BEFORE UPDATE ON public.navigation_items
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_images_updated_at
    BEFORE UPDATE ON public.images
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER set_thumbnail_url
    BEFORE INSERT ON public.images
    FOR EACH ROW
    EXECUTE FUNCTION public.generate_thumbnail_url();

CREATE TRIGGER update_site_content_updated_at
    BEFORE UPDATE ON public.site_content
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.category_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.navigation_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.site_content ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies
CREATE POLICY "Allow public read access" ON public.categories
    FOR SELECT USING (true);

CREATE POLICY "Allow public read access" ON public.category_items
    FOR SELECT USING (true);

CREATE POLICY "Allow public read access" ON public.navigation_items
    FOR SELECT USING (true);

CREATE POLICY "Allow public read access" ON public.images
    FOR SELECT USING (true);

CREATE POLICY "Allow public read access" ON public.site_content
    FOR SELECT USING (true);

-- Create admin access policies
CREATE POLICY "Allow admin full access" ON public.categories
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON public.category_items
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON public.navigation_items
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON public.images
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin full access" ON public.site_content
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Create storage bucket for categories and gallery
INSERT INTO storage.buckets (id, name, public)
VALUES ('gallery', 'gallery', true);

-- Add storage RLS policies
CREATE POLICY "Allow public read access for gallery" ON storage.objects
    FOR SELECT USING (bucket_id = 'gallery');

CREATE POLICY "Allow admin to upload to gallery" ON storage.objects
    FOR INSERT USING (bucket_id = 'gallery' AND auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin to update gallery objects" ON storage.objects
    FOR UPDATE USING (bucket_id = 'gallery' AND auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Allow admin to delete gallery objects" ON storage.objects
    FOR DELETE USING (bucket_id = 'gallery' AND auth.jwt() ->> 'role' = 'admin');

-- Create thumbnails directory in gallery bucket
INSERT INTO storage.objects (bucket_id, name) VALUES ('gallery', 'thumbnails/');

-- Comments for future updates
/*
 * Database Schema Version: 1.1
 * Last Updated: 2024-04-01
 * 
 * Update History:
 * - 1.0: Initial schema creation with core tables and RLS policies
 * - 1.1: Added storage bucket for gallery and categories, added storage RLS policies
 *
 * How to use this file:
 * 1. Connect to your PostgreSQL database
 * 2. Run this entire script to set up the schema
 * 3. For updates, new versions will be added at the bottom with version numbers
 */
