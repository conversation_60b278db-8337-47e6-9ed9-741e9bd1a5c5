// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vbkgqcdvbijtlcooiuga.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZia2dxY2R2YmlqdGxjb29pdWdhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ4ODg4MjIsImV4cCI6MjA1MDQ2NDgyMn0.SuMkm3NllT0Jy82Mpy02y-E2w3ud48UTvM2woBZRY5Y";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);