@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;
    --primary: 38 35% 63%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 38 35% 63%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 38 35% 63%;
    --radius: 0.5rem;
  }

  * {
    @apply border-border scrollbar-none;
  }

  body {
    @apply bg-background text-foreground font-sans scrollbar-none;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }

  /* Hide scrollbars but keep functionality */
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  /* Magazine Styles */
  .magazine {
    background-size: 100% 100%;
  }

  .magazine .page {
    background-color: white;
    background-size: 100% 100%;
  }

  .magazine .hard {
    background-color: #1A1A1A;
    color: white;
  }

  /* Turn.js Shadows */
  .turn-shadow {
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
  }

  .turn-page-wrapper {
    perspective: 2000px;
  }

  /* Page Transitions */
  .page-wrapper {
    transition: transform 0.5s;
  }
}

.hero-section {
  background: linear-gradient(rgba(26, 26, 26, 0.7), rgba(26, 26, 26, 0.7));
  background-size: cover;
  background-position: center;
}

.fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease-out forwards;
}

.slide-in {
  transform: translateX(-100%);
  animation: slideIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}