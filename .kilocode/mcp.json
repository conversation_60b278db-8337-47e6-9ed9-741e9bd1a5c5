{"mcpServers": {"Desktop Commander": {"command": "npx desktop-commander", "args": [], "env": {}, "fromGalleryId": "wonderwhy-er.<PERSON>"}, "Sequential Thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "fromGalleryId": "modelcontextprotocol.servers_sequentialthinking"}, "Playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {}, "fromGalleryId": "executeautomation.mcp-playwright", "alwaysAllow": []}, "woocommerce": {"command": "node", "args": ["/root/zeofshop/woocommerce-mcp-server/build/src/index.js"], "env": {"WORDPRESS_SITE_URL": "https://denzelblake.com", "WOOCOMMERCE_CONSUMER_KEY": "ck_502fe1e2975251740660e4bf25cb211e18550de3", "WOOCOMMERCE_CONSUMER_SECRET": "cs_d9b61545f2574f869e90d3c46c246c81002b8fc4"}, "alwaysAllow": ["get_products", "run_system_status_tool", "get_system_status_tools", "get_product", "create_product", "update_product", "delete_product", "get_orders", "get_order", "create_order", "update_order", "delete_order", "get_customers", "get_customer", "update_coupon", "get_product_category", "delete_customer", "update_customer", "get_product_categories", "get_sales_report", "get_coupons", "delete_product_category", "create_customer", "update_product_category", "create_product_category"]}, "wordpress-mcp": {"command": "npx", "args": ["-y", "@automattic/mcp-wordpress-remote@latest"], "env": {"WP_API_URL": "https://denzelblake.com", "WP_API_USERNAME": "zeof", "WP_API_PASSWORD": "mBrc xy2y 6ijb Z6rg Dut0 XBqu", "LOG_FILE": "/logs"}, "disabled": false, "alwaysAllow": []}, "mcp-mistral-ocr": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@everaldo/mcp-mistral-ocr", "--key", "397c5c2b-ac17-4224-91fa-7c04387813f0", "--profile", "meaningful-chicken-Iwuy4b"], "env": {"MISTRAL_API_KEY": "qZb61Qb0tqbdoAMsOA54zEvsz9CMMgXW", "OCR_DIR": "/root/zeofshop/"}, "alwaysAllow": []}}}