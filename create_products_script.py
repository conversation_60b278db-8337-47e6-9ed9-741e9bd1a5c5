import json
import asyncio
from typing import Dict, Any

# Assuming use_mcp_tool is globally available or imported from the environment

file_content = """
[
    {
        "product_name": "Lockpad",
        "description": "<PERSON><PERSON> Locked in time and the moment, this silky wool African suit magnifies your persona",
        "color": "Brown",
        "price": 150000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "Raffarian",
        "description": "Raffarian Safari This masterpiece collection embodies a harmonious blend of spirituality, creativity and craftsmanship; inspired by the rich Nigerian heritage.",
        "color": "Black",
        "price": 135000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "Spider Cut",
        "description": "Where sensation meets fashion, this piece is crafted from threads of an infant spider.",
        "color": [
            "Blue-Green/Burgundy"
        ],
        "price": 280000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "Maze Nave",
        "description": "A royal purple African Suit with a majestic inclination",
        "color": "Purple",
        "price": 135000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "CrissCross",
        "description": "Features a structured three-quarter sleeve top with a distinctive asymmetrical flap that buttons across the chest, matched with tapered trousers to complete the ensemble with a polished yet relaxed fit",
        "color": "Red",
        "price": 140000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "Bluelibrium",
        "description": "Made from top quality fabric. This royal blue Africa attire features three identical, intricately hand-stitched embroideries—an emblem of unity, balance and refined artistry",
        "color": "Blue",
        "price": 135000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "African Senate",
        "description": "This piece embodies both sophistication and heritage, with its flawless finish and subtle detailing. It speaks purity and confidence",
        "color": "White",
        "price": 135000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "AFRICAN SUIT"
    },
    {
        "product_name": "Von single-breasted overcoat",
        "description": "Crafted with a sleek silhouette with a single-button closure, structured shoulders, and a tailored long-line cut that elongates the frame.",
        "color": "Black",
        "price": 145000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "FOREIGN SUIT"
    },
    {
        "product_name": "Von floral single-breasted overcoat",
        "description": "Featuring a tailored fit with a sleek single-breasted closure, this overcoat is adorned with a refined floral print that adds artistic flair without compromising on sophistication.",
        "color": "Black",
        "price": 155000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLDOUT",
        "category": "FORMAL WEARS",
        "subcategory": "FOREIGN SUIT"
    },
    {
        "product_name": "Suit vest",
        "description": "Cut from high-quality wool fabric for comfort and durability, it includes a U-neckline with front button closure",
        "color": "Black",
        "price": 55000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "FOREIGN SUIT"
    },
    {
        "product_name": "Floral suit vest",
        "description": "Features U-neckline with frontal button closure and a refined floral pattern that elevates any formal or semi-formal ensemble.",
        "color": "Black",
        "price": 65000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "FOREIGN SUIT"
    },
    {
        "product_name": "Leat vintage straight-leg pants",
        "description": "Crafted with high quality wool fabric, featuring front pleats for added structure and a straight-leg cut—offering a clean, elongated silhouette, and lasting comfort.",
        "color": [
            "Black",
            "White"
        ],
        "price": 85000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "FOREIGN SUIT"
    },
    {
        "product_name": "Masquerade",
        "description": "Features a crisp white shirt adorned with a black collar and eye-catching black prints that add a touch of artistry edge,the look is further elevatedby unique white singly cut fabric panels that subtly flare with each step",
        "color": "White",
        "price": 180000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Work of Art",
        "description": "This sleek set features a fitted,3-quarter collar top with a vibrant color splash design across the front —adding the perfect pop of personality against the deep black base. Paired with matching bottoms, the look blends comfort, edge, and modern flair. Made for those who lead with confidence.",
        "color": "Black",
        "price": 145000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Floral Motion",
        "description": "Featuring delicate floral embroidery on the chest, each set blends intrinsic details with Modern Sophistication",
        "color": "Blue",
        "price": 115000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLDOUT",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Petal Crest",
        "description": "Featuring delicate floral embroidery on the chest, each set blends intrinsic details with Modern Sophistication",
        "color": "Light Blue",
        "price": 115000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLDOUT",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Sleeve Mark",
        "description": "This collared tracksuit is your laid-back answer to looking put-together. With a sleek zip-up top and matching bottoms, it’s the perfect combo of sporty and smart",
        "color": "Red",
        "price": 115000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Tropicana",
        "description": "3-quarter top with a vibrant tropical design across the front —adding the perfect pop of personality. Paired with matching bottoms, the look blends comfort, edge, and modern flair. Made for those who lead with confidence.",
        "color": "Black",
        "price": 110000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Send Down the Rain (SDR)",
        "description": "Featuring delicate lined embroidery on the chest, each set blends intrinsic details with Modern Sophistication",
        "color": "Blue",
        "price": 120000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Velox",
        "description": "This collared tracksuit is your laid-back answer to looking put-together. With a sleek zip-up top and matching bottoms, it’s the perfect combo of sporty and smart",
        "color": "Black",
        "price": 120000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Flapped Collar",
        "description": "This collared shirt with pairing pants, features a unique front flap detail with buttons that adds subtle structure and standout style. It effortlessly bridges the gap between polished and contemporary.",
        "color": "Brown",
        "price": 125000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Spotted Safari",
        "description": "Antique two piece: collar top and shorts, retro & classy with a specific targeted design",
        "color": "Light Blue",
        "price": 95000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "White Streak",
        "description": "Features a deep blue top accented with a white striped sewn fabric for a modern, tailored touch. Paired with crisp white pants, this ensemble strikes the perfect balance between bold and clean.",
        "color": "Blue",
        "price": 105000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Line Mark",
        "description": "Features a white top accented with a blue striped sewn fabric for a modern, tailored touch. Paired with crisp blue pants, this ensemble strikes the perfect balance between bold and clean.",
        "color": "White",
        "price": 120000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORDS ETS"
    },
    {
        "product_name": "Leat Netted Female Suit",
        "description": "Made with lightweight, breathable fabric, this easygoing two-piece features a relaxed fit and a classic collar for that perfect balance of laid-back and stylish",
        "color": "Beige",
        "price": 105000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "CO-ORD SETS"
    },
    {
        "product_name": "Zarabian",
        "description": "Features intricate Arabian-inspired patterns that tell a story of tradition and style. The crisp black collar adds a modern contrast, grounding the ornate design with a sharp, contemporary edge.",
        "color": "Black",
        "price": 75000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Zig-Zagged",
        "description": "This short sleeve collared shirt features a dynamic zig-zag design that plays with contrast and motion, perfect for standing out with effortless edge.",
        "color": "White & Black",
        "price": 70000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Stay exclusive",
        "description": "The rich color adds depth, while the clean collar and standout logos give it that effortlessly cool, street-smart edge.",
        "color": "Burgundy",
        "price": 75000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Zebra",
        "description": "Features and sleek full-front zip design and a bold zig-zag pattern that plays with contrast and motion, perfect for standing out with effortless edge.",
        "color": "Green",
        "price": 85000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Zimono",
        "description": "This kimono top features ornate design with a sharp, contemporary edge.",
        "color": "Blue",
        "price": 85000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Net Summer",
        "description": "Blends sheer net fabric with a structured collar neck for a bold and elegant look. Airy and lightweight, it teases just the right amount of skin while keeping things effortlessly chic.",
        "color": "White",
        "price": 90000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Zimono B&W",
        "description": "Redefines elegance with a modern twist. Crafted from lightweight netted fabric, it offers a sheer, breathable feel while the bold monochrome palette keeps it striking.",
        "color": "Black & White",
        "price": 85000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Canvas Line",
        "description": "Features vertically aligned tonal fabric lines that add depth and quiet detail to the monochrome palette.",
        "color": "Beige",
        "price": 85000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "NoirBloom Vest",
        "description": "Crafted from a rich fabric laced with subtle black floral patterns, this sleeveless shirt redefines classic structure with a black collar and sleeve buttonless slight chest opening for a relaxed confident silhouette.",
        "color": "Oxblood",
        "price": 75000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "AVAILABLE",
        "category": "FORMAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Bongo paint",
        "description": "Feature a clean, tailored silhouette with a hidden twist: a unique beneath-the-surface design that reveals itself with every step.",
        "color": "Black",
        "price": 75000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "PANTS"
    },
    {
        "product_name": "Rope slit",
        "description": "Designed with a laid-back fit and a rope-tie waist for easy, adjustable comfort.",
        "color": "Blue",
        "price": 70000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "FORMAL WEARS",
        "subcategory": "PANTS"
    },
    {
        "product_name": "Zeof Fly High Tee",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": "Black",
        "price": 55000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "OYS",
        "description": "Soft premium cotton for a luxurious feel and all-day comfort, with a relaxed silhouette for effortless styling and versatility",
        "color": "Black",
        "price": 55000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Zrilled",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": "Black",
        "price": 50000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Zeof Skeleton Tees",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": "Grey",
        "price": 70000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "ZEOF X3 Tee",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": [
            "Black",
            "White",
            "Peach"
        ],
        "price": 50000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Zeof Dance Tee",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": "Grey",
        "price": 70000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Zeof Comrade Tees",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": "Browm",
        "price": 60000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "DAWIGMF Tee",
        "description": "Designed with a clean silhouette, breathable cotton fabric",
        "color": [
            "White",
            "Black"
        ],
        "price": 55000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Signature top",
        "description": "A timeless essential crafted for comfort and versatility. Made from premium, breathable cotton, this tee features a tailored fit, soft-touch feel, and durable stitching for lasting wear.",
        "color": [
            "Black",
            "White"
        ],
        "price": 50000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Signature tank top",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": "Black",
        "price": 40000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Signature tank top II",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": "Black",
        "price": 40000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Zeof Bloc",
        "description": "A timeless essential crafted for comfort and versatility. Made from premium, breathable cotton, this tee features a tailored fit, soft-touch feel, and durable stitching for lasting wear.",
        "color": [
            "Beige",
            "Black",
            "White"
        ],
        "price": 55000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "dIb MMXX crop top",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": [
            "White",
            "Black",
            "Light Grey/Black",
            "Dark Grey/Black",
            "White/Pink"
        ],
        "price": 35000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": [
            "1S",
            "1M",
            "1L",
            "1XL"
        ],
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Zeof 85 Skeleton crop top",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": "White",
        "price": 35000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Exclusive crop top",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": [
            "Black",
            "White",
            "Blue",
            "Peach"
        ],
        "price": 35000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Signature tank top",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": [
            "Red",
            "Black",
            "White",
            "Brown"
        ],
        "price": 25000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Cosmic Noir",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": [
            "Black",
            "White"
        ],
        "price": 45000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": [
            "2M"
        ],
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Starlight",
        "description": "Flattering fit, breathable cotton fabric, Effortless versatility",
        "color": [
            "White",
            "Pink"
        ],
        "price": 50000,
        "size": [
            "S",
            "M",
            "L",
            "XL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "TEE"
    },
    {
        "product_name": "Venom Zipper",
        "description": "Features sleek side zippers that open to proudly reveal db. Designed for comfort and individuality, these pants blend function with fashion—crafted from durable, high-quality fabric with a tailored fit.",
        "color": "Black",
        "price": 70000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL",
            "XXXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "PANTS"
    },
    {
        "product_name": "Desert Pants",
        "description": "Features black fabric pieces artfully sewn in for added dimension and texture. Ideal for both casual and upscale styling, these pants are where minimalism meets expressive design.",
        "color": "Grey",
        "price": 90000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL",
            "XXXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "PANTS"
    },
    {
        "product_name": "Marble pants",
        "description": "Reimagined with edgy, modern details, featuring striking blue patchwork beneath the front of both legs and across the lap.",
        "color": [
            "Blue",
            "Grey"
        ],
        "price": 85000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL",
            "XXXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "PANTS"
    },
    {
        "product_name": "Signature Free Pants",
        "description": "Crafted with a breathable fabric, designed for movement and ease. Featuring adjustable rope-tie fastenings for custom fit at the waist.",
        "color": [
            "Black",
            "Grey",
            "Blue",
            "Green"
        ],
        "price": 55000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL",
            "XXXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "PANTS"
    },
    {
        "product_name": "Zeof Zlit",
        "description": "Made with lightweight, breathable fabric, this easygoing piece features a relaxed fit and a classic collar for that perfect balance of laid-back and stylish",
        "color": [
            "Green/Orange",
            "Black/Red"
        ],
        "price": 60000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "85 Exclusive",
        "description": "Made with a premium breathable fabric, this easygoing piece features a relaxed fit and a classic collar for that perfect balance of laid-back and stylish",
        "color": [
            "Black",
            "White"
        ],
        "price": 65000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Brown Matter",
        "description": "Crafted with a sleek fusion of sporty edge and modern style. Featuring a structured collar and bold front zipper, this tank delivers a clean silhouette with an athletic twist",
        "color": "Brown",
        "price": 50000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "SHIRT"
    },
    {
        "product_name": "Invader",
        "description": "Made with durable fabric with comfort stretch. Features a bold cape overlay, functional side pockets, statement prints, and a relaxed fit that hits hard from every angle. Paired with double-waisted pants with rope detailing and printed accents",
        "color": [
            "Black",
            "Grey"
        ],
        "price": 150000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "HOODIES"
    },
    {
        "product_name": "Rooted",
        "description": "Features a striking root-inspired pattern that brings nature’s raw energy to urban fashion.",
        "color": [
            "Brown",
            "Blue & Green"
        ],
        "price": 60000,
        "size": [
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "CASUAL WEARS",
        "subcategory": "HOODIES"
    },
    {
        "product_name": "Exclusive bodyed top",
        "description": "Hugs every curve with confident support and a sleek, second-skin fit. Featuring a classic round neckline and a cropped cut that keeps things cool, it’s the perfect blend of performance and style—made to power you through workouts or elevate your athleisure game",
        "color": [
            "Red",
            "White",
            "Black",
            "Pink"
        ],
        "price": 40000,
        "size": [
            "S",
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "ACTIVEWEAR",
        "subcategory": null
    },
    {
        "product_name": "Exclusive fit biker shorts",
        "description": "Flattering high-waisted fit and a smooth, stretch fabric that hugs in all the right places",
        "color": [
            "Black",
            "Grey"
        ],
        "price": 50000,
        "size": [
            "S",
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "ACTIVEWEAR",
        "subcategory": null
    },
    {
        "product_name": "Exclusive fit bra top + sport shorts",
        "description": "The bra offers a snug, secure fit with just the right amount of stretch, while the matching high-waist shorts hug your curves for a streamlined, no-slip silhouette.",
        "color": [
            "Red",
            "Black"
        ],
        "price": 80000,
        "size": [
            "S",
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "ACTIVEWEAR",
        "subcategory": null
    },
    {
        "product_name": "Exclusive fit shorts",
        "description": "The high-waist shorts hug your curves for a streamlined, no-slip silhouette.",
        "color": [
            "Black",
            "White",
            "Blue",
            "Pink"
        ],
        "price": 45000,
        "size": [
            "S",
            "M",
            "L",
            "XL",
            "XXL"
        ],
        "status": "SOLD OUT",
        "category": "ACTIVEWEAR",
        "subcategory": null
    },
    {
        "product_name": "Zeof Classic Snapback",
        "description": "Featuring bold logo detailing on the front, side, and back panels, this cap blends iconic style with 360° dbi presence. With its structured fit, flat brim, and adjustable snap closure, it is the ultimate finishing touch for any street-smart look.",
        "color": [
            "Black/Red",
            "Black",
            "White",
            "Blue"
        ],
        "price": 35000,
        "size": [
            "NIL"
        ],
        "status": "SOLD OUT",
        "category": "HEAD WEARS",
        "subcategory": null
    },
    {
        "product_name": "dIb MMXX Snapback",
        "description": "Featuring bold logo detailing on the front, side, and back panels, this cap blends iconic style with 360° dbi presence. With its structured fit, flat brim, and adjustable/non-adjustable snap closure, it is the ultimate finishing touch for any street-smart look.",
        "color": [
            "Black",
            "White",
            "Red/Black",
            "Blue"
        ],
        "price": 45000,
        "size": [
            "NIL"
        ],
        "status": "SOLD OUT",
        "category": "HEAD WEARS",
        "subcategory": null
    },
    {
        "product_name": "Zeof X Snapback",
        "description": "Featuring bold logo detailing on the front, side, and back panels, this cap blends iconic style with 360° dbi presence. With its structured fit, flat brim, and adjustable/ non-adjustable snap closure, it is the ultimate finishing touch for any street-smart look.",
        "color": [
            "Black/Yellow",
            "Black",
            "Blue"
        ],
        "price": 40000,
        "size": [
            "NIL"
        ],
        "status": "SOLD OUT",
        "category": "HEAD WEARS",
        "subcategory": null
    },
    {
        "product_name": "dIb MMXX Headband",
        "description": "Premium Knit Fabric, comfortable and secure fit",
        "color": [
            "Black",
            "White",
            "Red",
            "Grey"
        ],
        "price": 15000,
        "size": [
            "NIL"
        ],
        "status": [
            "1 BLACK",
            "1 RED"
        ],
        "category": "HEAD WEARS",
        "subcategory": null
    },
    {
        "product_name": "Chelsea boot",
        "description": "Feature a sleek silhouette, elastic side panels, and a pull-on tab for effortless wear.",
        "color": [
            "White",
            "Black"
        ],
        "price": 115000,
        "size": [
            "36-47"
        ],
        "status": "SOLD OUT",
        "category": "FOOT WEARS",
        "subcategory": null
    },
    {
        "product_name": "Zein Half Shoe",
        "description": "Feature a smooth, premium leather finish, cushioned insole, and a streamlined silhouette that slips on effortlessly.",
        "color": [
            "Black",
            "Brown"
        ],
        "price": 85000,
        "size": [
            "36-47"
        ],
        "status": "SOLD OUT",
        "category": "FOOT WEARS",
        "subcategory": null
    }
]
"""
product_data = json.loads(file_content)

existing_categories = [
  {
    "id": 86,
    "name": "Accessories",
    "slug": "accessories",
    "parent": 0,
    "description": "Shop through our latest selection of Accessories.",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/86",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 179,
    "name": "Bags",
    "slug": "bags",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/179",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 127,
    "name": "Bottom",
    "slug": "bottom",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/127",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 126,
    "name": "Denim",
    "slug": "denim",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/126",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 123,
    "name": "Dress",
    "slug": "dress",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/123",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 180,
    "name": "Footwear",
    "slug": "footwear",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/180",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 125,
    "name": "Jackets",
    "slug": "jackets",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/125",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 87,
    "name": "Jewellery",
    "slug": "jewellery",
    "parent": 0,
    "description": "Shop through our latest selection of Jewellery.",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/87",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 182,
    "name": "Man",
    "slug": "man",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/182",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 9,
    "name": "Men",
    "slug": "men",
    "parent": 0,
    "description": "Shop through our latest selection of Men’s Clothing and Accessories.",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/9",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 11,
    "name": "Shoes",
    "slug": "shoes",
    "parent": 0,
    "description": "Free shipping for all order over $100",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/11",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 163,
    "name": "Swim",
    "slug": "swim",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/163",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 132,
    "name": "T-Shirt",
    "slug": "t-shirt",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/132",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 124,
    "name": "Tops",
    "slug": "tops",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/124",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 186,
    "name": "Uncategorized",
    "slug": "uncategorized",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/186",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 187,
    "name": "Woman",
    "slug": "woman",
    "parent": 0,
    "description": "",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/187",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  },
  {
    "id": 122,
    "name": "Women",
    "slug": "women",
    "parent": 0,
    "description": "Shop through our latest selection of Women’s Clothing and Accessories.",
    "display": "default",
    "image": None,
    "menu_order": 0,
    "count": 0,
    "_links": {
      "self": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories/122",
          "targetHints": {
            "allow": [
              "GET",
              "POST",
              "PUT",
              "PATCH",
              "DELETE"
            ]
          }
        }
      ],
      "collection": [
        {
          "href": "https://denzelblake.com/wp-json/wc/v3/products/categories"
        }
      ]
    }
  }
]


unique_categories_from_json = {}
for product in product_data:
    main_category = product['category'].strip()
    sub_category = (product.get('subcategory') or '').strip()

    if main_category not in unique_categories_from_json:
        unique_categories_from_json[main_category] = []
    
    if sub_category and sub_category not in unique_categories_from_json[main_category]:
        unique_categories_from_json[main_category].append(sub_category)

category_ids = {cat['name']: cat['id'] for cat in existing_categories}

async def create_categories_and_subcategories():
    for main_cat_name, sub_cat_names in unique_categories_from_json.items():
        # Check if main category exists
        if main_cat_name not in category_ids:
            print(f"Creating main category: {main_cat_name}")
            response = await use_mcp_tool(
                server_name='woocommerce',
                tool_name='create_product_category',
                arguments={"categoryData": {"name": main_cat_name}}
            )
            category_ids[main_cat_name] = response['id']
        
        # Create subcategories
        for sub_cat_name in sub_cat_names:
            if sub_cat_name not in category_ids:
                print(f"Creating subcategory: {sub_cat_name} under {main_cat_name}")
                response = await use_mcp_tool(
                    server_name='woocommerce',
                    tool_name='create_product_category',
                    arguments={"categoryData": {"name": sub_cat_name, "parent": category_ids[main_cat_name]}}
                )
                category_ids[sub_cat_name] = response['id']

async def create_products():
    for product in product_data:
        product_name = product['product_name']
        description = product['description']
        price = str(product['price'])
        categories_for_product = []

        main_category_name = product['category'].strip()
        if main_category_name in category_ids:
            categories_for_product.append({"id": category_ids[main_category_name]})

        sub_category_name = (product.get('subcategory') or '').strip()
        if sub_category_name and sub_category_name in category_ids:
            categories_for_product.append({"id": category_ids[sub_category_name]})
        
        product_status = product['status']

        print(f"Creating product: {product_name}")
        await use_mcp_tool(
            server_name='woocommerce',
            tool_name='create_product',
            arguments={
                "productData": {
                    "name": product_name,
                    "description": description,
                    "regular_price": price,
                    "categories": categories_for_product,
                    "status": "publish" if product_status == "AVAILABLE" else "draft"
                }
            }
        )

async def main():
    await create_categories_and_subcategories()
    await create_products()

if __name__ == "__main__":
    asyncio.run(main())