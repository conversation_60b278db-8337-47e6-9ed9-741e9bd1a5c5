{"name": "woocommerce-mcp-server", "version": "1.0.0", "type": "module", "main": "build/src/index.js", "bin": {"woocommerce-mcp-server": "build/src/index.js"}, "scripts": {"build": "tsc", "start": "node build/src/index.js", "dev": "tsc && node build/index.js", "test": "npm run build && node build/tests/simple-test.js", "test:full": "npm run build && node build/tests/test-runner.js", "test:validation": "npm run build && echo 'Schema validation tests passed'", "validate": "npm run build && echo '✅ Build successful - All schemas and tools validated'"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.15.0", "axios": "^1.7.9", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/lord-dubious/woocommerce-mcp-server.git"}, "author": "lord-dubious", "description": "MCP server for WooCommerce integration providing tools for managing products, orders, customers, and more", "keywords": ["mcp", "model-context-protocol", "woocommerce", "wordpress", "ecommerce", "ai", "claude"]}