# WooCommerce MCP Server Test Configuration
# Copy this file to .env in the project root and fill in your test credentials

# Test WordPress/WooCommerce Site
TEST_WORDPRESS_SITE_URL=https://your-test-site.com

# WooCommerce API Credentials (for testing)
# Get these from WooCommerce > Settings > Advanced > REST API
TEST_WOOCOMMERCE_CONSUMER_KEY=ck_your_test_consumer_key_here
TEST_WOOCOMMERCE_CONSUMER_SECRET=cs_your_test_consumer_secret_here

# WordPress Credentials (optional, for WordPress API testing)
TEST_WORDPRESS_USERNAME=your_test_username
TEST_WORDPRESS_PASSWORD=your_test_password

# Test Configuration
TEST_TIMEOUT=30000
TEST_RETRY_COUNT=3
TEST_DELAY_BETWEEN_REQUESTS=100

# Note: Use a test/staging site, not your production site!
# These tests will create, update, and delete data in your WooCommerce store.
