export const ImageConstant = {
  Tooths: require('../../assets/Images/Tooths.png'),
  header_img: require('../../assets/Images/headerimg.png'),
  backarrow: require('../../assets/Images/arrow.png'),
  // profile: require('../../assets/Images/Profile.png'),
  dentist: require('../../assets/Images/Dentist.png'),
  sms: require('../../assets/Images/sms.png'),
  lock: require('../../assets/Images/lock.png'),
  checkbox: require('../../assets/Images/checkbox.png'),
  checkboxblue: require('../../assets/Images/checkboxblue.png'),
  apple: require('../../assets/Images/apple.png'),
  eye: require('../../assets/Images/eye.png'),
  eyeslash: require('../../assets/Images/eye-slash.png'),
  gallerypic1: require('../../assets/Images/Gallerypic1.png'),
  gallerypic2: require('../../assets/Images/Gallerypic2.png'),
  gallerypic3: require('../../assets/Images/Gallerypic3.png'),
  buildings: require('../../assets/Images/buildings.png'),
  state: require('../../assets/Images/state.png'),
  global: require('../../assets/Images/global.png'),
  message: require('../../assets/Images/message.png'),
  userlogo: require('../../assets/Images/userlogo.png'),
  skip_btn: require('../../assets/Images/skip_btn.png'),
  intro_img1: require('../../assets/Images/Introimg.png'),
  intro_img2: require('../../assets/Images/introscreen.png'),
  intro_img3: require('../../assets/Images/intro.png'),
  Ellipse: require('../../assets/Images/Ellipse.png'),
  Ellipse_Top: require('../../assets/Images/Ellipsetop.png'),
  arrow_right: require('../../assets/Images/arrow-right.png'),
  export: require('../../assets/Images/export.png'),
  call: require('../../assets/Images/call.png'),
  ticket: require('../../assets/Images/ticket.png'),
  cancel: require('../../assets/Images/cancel.png'),
  tickCircle: require('../../assets/Images/tick-circle.png'),
  cross: require('../../assets/Images/cross.png'),
  gallerypic4: require('../../assets/Images/Gallerypic4.png'),
  calendar: require('../../assets/Images/calendar.png'),
  arrow_down: require('../../assets/Images/arrowright_icon.png'),
  tooltip_image: require('../../assets/Images/tooltipimage.png'),
  arrow_tooltip: require('../../assets/Images/arrowTooltip.png'),
  gender_icon: require('../../assets/Images/gender_icon.png'),
  radioButtonOn: require('../../assets/Images/radio_buttonOn.png'),
  radioButtonOff: require('../../assets/Images/radio_buttonOff.png'),
  check_icon: require('../../assets/Images/check.png'),
  unCheck_icon: require('../../assets/Images/unCheck.png'),
  medical_condition: require('../../assets/Images/medicalCondition.png'),
  success_icon: require('../../assets/Images/Symbol.png'),
  // homeTab_icon: require('../../assets/Images/Home.png'),
  // profileTab_icon: require('../../assets/Images/Profile.png'),
  clipboardTab_icon: require('../../assets/Images/clipboard-text.png'),
  searchTab_icon: require('../../assets/Images/search-normal.png'),
  tab_background: require('../../assets/Images/tabBackgroundImage.png'),
  home_tab: require('../../assets/Images/homeTab.png'),
  appointment_tab: require('../../assets/Images/Appointments.png'),
  search_tab: require('../../assets/Images/searchTab.png'),
  profile_tab: require('../../assets/Images/profileTab.png'),
  backgroundScreen_image: require('../../assets/Images/backgroundImage.png'),
  myDevices: require('../../assets/Images/Frame.png'),
  notification: require('../../assets/Images/notification.png'),
  profile_delete: require('../../assets/Images/profile-delete.png'),
  lock_small: require('../../assets/Images/lockSmall.png'),
  deviceFrame: require('../../assets/Images/deviceFram.png'),
  headerImage: require('../../assets/Images/headerImage.png'),
  scanner: require('../../assets/Images/scan.png'),
  logOut: require('../../assets/Images/logout.png'),
  profileOfDR: require('../../assets/Images/doctor_profile.png'),
  heart: require('../../assets/Images/heart.png'),
  profileAccount: require('../../assets/Images/profileAccount.png'),
  wallet: require('../../assets/Images/wallet.png'),
  documentProfile: require('../../assets/Images/documentProfile.png'),
  messageProfile: require('../../assets/Images/messages_icon.png'),
  document_text: require('../../assets/Images/document-text.png'),
  security: require('../../assets/Images/security-safe.png'),
  contactUs: require('../../assets/Images/messagesProfile.png'),
  setting: require('../../assets/Images/settingProfile.png'),
  ContactUsImg: require('../../assets/Images/contactUsImg.png'),
  Copy: require('../../assets/Images/copy.png'),
  tabBackground: require('../../assets/Images/tabBackground.png'),
  introBackground: require('../../assets/Images/introBackgroung.png'),
  edit: require('../../assets/Images/edit.png'),
  intro1: require('../../assets/Images/introscreen1.png'),
  intro2: require('../../assets/Images/introscreen2.png'),
  intro3: require('../../assets/Images/introscreen3.png'),
  ic_login: require('../../assets/Images/ic_login.png'),
  email: require('../../assets/Images/email.png'),
  chooseRole: require('../../assets/Images/chooseRole.png'),
  Partner: require('../../assets/Images/Partner.png'),
  Patient: require('../../assets/Images/Patient.png'),
  arrow_left: require('../../assets/Images/arrowleft_icon.png'),
  blur: require('../../assets/Images/blur.png'),
  SignUp: require('../../assets/Images/signup.png'),
  docProfile: require('../../assets/Images/DocProfile.png'),
  camera: require('../../assets/Images/camera.png'),
  doc: require('../../assets/Images/doc.png'),
  person: require('../../assets/Images/Person.png'),
  Congratulations: require('../../assets/Images/Congratulations.png'),
  Training: require('../../assets/Images/Training.png'),
  Settings: require('../../assets/Images/Settings.png'),
  Manage_Services: require('../../assets/Images/ManageServices.png'),
  Wallet: require('../../assets/Images/wallet.png'),
  Redeem: require('../../assets/Images/Redeem.png'),
  Appointments: require('../../assets/Images/Appointments.png'),
  Leads: require('../../assets/Images/Leads.png'),
  Employee_Contract: require('../../assets/Images/EmployeeContract.png'),
  Time_Sheet: require('../../assets/Images/TimeSheet.png'),
  Performance_Review: require('../../assets/Images/PerformanceReview.png'),
  Training_Records: require('../../assets/Images/TrainingRecords.png'),
  Qualification: require('../../assets/Images/Qualification.png'),
  Indemnity_Form: require('../../assets/Images/IndemnityForm.png'),
  Payroll_Information: require('../../assets/Images/PayrollInformation.png'),
  Incident_Report: require('../../assets/Images/IncidentReport.png'),
  Employee_Information: require('../../assets/Images/EmployeeInformation.png'),
  Employee_Handbook: require('../../assets/Images/EmployeeHandbook.png'),
  Training_Upskilling: require('../../assets/Images/Training&Upskilling.png'),
  Wellness_Program: require('../../assets/Images/WellnessProgram.png'),
  Join_My_Peers: require('../../assets/Images/JoinMyPeers.png'),
  FAQ: require('../../assets/Images/FAQ.png'),
  Payment: require('../../assets/Images/Payment.png'),
  star: require('../../assets/Images/star.png'),
  MyDashboard: require('../../assets/Images/My_Dashboard.png'),
  MyAppointments: require('../../assets/Images/My_appointments.png'),
  MyProfile: require('../../assets/Images/My_Profile.png'),
  MyWallet: require('../../assets/Images/My_Wallet.png'),
  MyReferralCode: require('../../assets/Images/My_referral_code.png'),
  VerifyMyProfile: require('../../assets/Images/Verify_my_profile.png'),
  Chat: require('../../assets/Images/Chat.png'),
  phone: require('../../assets/Images/Phone.png'),
  Booking: require('../../assets/Images/Booking.png'),
  drawer: require('../../assets/Images/drawer.png'),
  nurse_icon: require('../../assets/Images/nurse_icon.png'),
  doctor_icon: require('../../assets/Images/doctor_icon.png'),
  dentist_icon: require('../../assets/Images/dentist_icon.png'),
  Membership: require('../../assets/Images/Membership.png'),
  Physiotherapist: require('../../assets/Images/Physiotherapist.png'),
  Equipment: require('../../assets/Images/Equipment.png'),
  lab: require('../../assets/Images/lab.png'),
  x_ray: require('../../assets/Images/x-ray.png'),
  ecg: require('../../assets/Images/ecg.png'),
  health: require('../../assets/Images/health.png'),
  bus: require('../../assets/Images/bus.png'),
  attendant_icon: require('../../assets/Images/attendant_icon.png'),
  searchNew: require('../../assets/Images/searchNew.png'),
  MyAppointments: require('../../assets/Images/MyAppointments.png'),
  Searchservices: require('../../assets/Images/Searchservices.png'),
  Payments: require('../../assets/Images/Payments.png'),
  Myhealthrecord: require('../../assets/Images/Myhealthrecord.png'),
  MangeSetting: require('../../assets/Images/MangeSetting.png'),
  LogoutNew: require('../../assets/Images/LogoutNew.png'),
  invite: require('../../assets/Images/invite.png'),
  faqNew: require('../../assets/Images/faqNew.png'),
  dashboard: require('../../assets/Images/dashboard.png'),
  contact: require('../../assets/Images/contact.png'),
  offer: require('../../assets/Images/offer.png'),
  verify: require('../../assets/Images/verify.png'),
  edit: require('../../assets/Images/edit.png'),
  intro1: require('../../assets/Images/introscreen1.png'),
  intro2: require('../../assets/Images/introscreen2.png'),
  intro3: require('../../assets/Images/introscreen3.png'),
  ic_login: require('../../assets/Images/ic_login.png'),
  email: require('../../assets/Images/email.png'),
  chooseRole: require('../../assets/Images/chooseRole.png'),
  Partner: require('../../assets/Images/Partner.png'),
  Patient: require('../../assets/Images/Patient.png'),
  arrow_left: require('../../assets/Images/arrowleft_icon.png'),
  blur: require('../../assets/Images/blur.png'),
  SignUp: require('../../assets/Images/signup.png'),
  docProfile: require('../../assets/Images/DocProfile.png'),
  camera: require('../../assets/Images/camera.png'),
  doc: require('../../assets/Images/doc.png'),
  person: require('../../assets/Images/Person.png'),
  Congratulations: require('../../assets/Images/Congratulations.png'),
  Training: require('../../assets/Images/Training.png'),
  Settings: require('../../assets/Images/Settings.png'),
  Manage_Services: require('../../assets/Images/ManageServices.png'),
  Wallet: require('../../assets/Images/wallet.png'),
  Redeem: require('../../assets/Images/Redeem.png'),
  Appointments: require('../../assets/Images/Appointments.png'),
  Leads: require('../../assets/Images/Leads.png'),
  Employee_Contract: require('../../assets/Images/EmployeeContract.png'),
  Time_Sheet: require('../../assets/Images/TimeSheet.png'),
  Performance_Review: require('../../assets/Images/PerformanceReview.png'),
  Training_Records: require('../../assets/Images/TrainingRecords.png'),
  Qualification: require('../../assets/Images/Qualification.png'),
  Indemnity_Form: require('../../assets/Images/IndemnityForm.png'),
  Payroll_Information: require('../../assets/Images/PayrollInformation.png'),
  Incident_Report: require('../../assets/Images/IncidentReport.png'),
  Employee_Information: require('../../assets/Images/EmployeeInformation.png'),
  Employee_Handbook: require('../../assets/Images/EmployeeHandbook.png'),
  Training_Upskilling: require('../../assets/Images/Training&Upskilling.png'),
  Wellness_Program: require('../../assets/Images/WellnessProgram.png'),
  Join_My_Peers: require('../../assets/Images/JoinMyPeers.png'),
  FAQ: require('../../assets/Images/FAQ.png'),
  Payment: require('../../assets/Images/Payment.png'),

  MyDashboard: require('../../assets/Images/My_Dashboard.png'),
  MyAppointments: require('../../assets/Images/My_appointments.png'),
  MyProfile: require('../../assets/Images/My_Profile.png'),
  MyWallet: require('../../assets/Images/My_Wallet.png'),
  MyReferralCode: require('../../assets/Images/My_referral_code.png'),
  VerifyMyProfile: require('../../assets/Images/Verify_my_profile.png'),
  Chat: require('../../assets/Images/Chat.png'),
  isCheck: require('../../assets/Images/isCheck.png'),
  // Logout: require("../../assets/Images/Logout.png"),
  graph:require("../../assets/Images/graph.png"),
  metar:require("../../assets/Images/metar.png"),
  share:require("../../assets/Images/share.png"),
  calendeer:require("../../assets/Images/calendeer.png"),
  status:require("../../assets/Images/status.png"),
  clock:require("../../assets/Images/clock.png"),
  video:require("../../assets/Images/video.png"),
  location:require("../../assets/Images/location.png"),
  fee:require("../../assets/Images/fee.png"),
  yearof:require("../../assets/Images/yearof.png"),
  
};
