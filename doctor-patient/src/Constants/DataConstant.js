export const medicationsData = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON> (<PERSON>umadin)",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON> (Xarelto)",
  },
  {
    id: 3,
    name: "<PERSON>bi<PERSON><PERSON> (Pradaxa)",
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON> (Li<PERSON><PERSON>)",
  },
  {
    id: 5,
    name: "<PERSON><PERSON><PERSON> (Bayer, Bufferin, Ecotrin)",
  },
  {
    id: 6,
    name: "<PERSON><PERSON><PERSON> (Bayer, Bufferin, Ecotrin)",
  },
];
export const cancerData = [
  {
    id: 1,
    name: "Chemotherapy (in the past 2 years)",
  },
  {
    id: 2,
    name: "Radiation Therapy (in the past 2 years)",
  },
  {
    id: 3,
    name: "Immunotherapy (in the past 2 years)",
  },
];
export const cancerTreatment = [
  {
    id: 1,
    name: "History of cancer",
  },
  {
    id: 2,
    name: "Past cancer diagnosis",
  },
];
export const cancerStage = [
  {
    value: "first",
    label: "first",
  },
  {
    value: "second",
    label: "second",
  },
  {
    value: "third",
    label: "third",
  },
];
export const chronicData = [
  {
    id: 1,
    name: "Liver problems / Disease",
  },
  {
    id: 2,
    name: "Kidney problems / Disease",
  },
  {
    id: 3,
    name: "Diabetes",
  },
  {
    id: 4,
    name: "Mental health conditions (e.g., seizures, epilepsy)",
  },
];
export const drugHistory = [
  {
    id: 1,
    name: "Tobacco Use",
  },
  {
    id: 2,
    name: "Alcohol use",
  },
];
export const cardiovascular = [
  {
    id: 1,
    name: "Chest Pain",
  },
  {
    id: 2,
    name: "Recent Stroke",
  },
];
export const osteonecrosisData = [
  {
    id: 1,
    name: "History of Osteonecrosis",
  },
  {
    id: 2,
    name: "Bisphosphonates",
  },
];
export const highFallData = [
  {
    id: 1,
    name: "Vertigo",
  },
  {
    id: 2,
    name: "Significant Visual Impairment",
  },
];
export const hearingData = [
  {
    id: 1,
    name: "Problems Hearing",
  },
  {
    id: 2,
    name: "Sinus Problems",
  },
];
export const painIssue = [
  {
    id: 1,
    name: "Neck Pain",
  },
  {
    id: 2,
    name: "Back Pain",
  },
];
export const mentalHealth = [
  {
    id: 1,
    name: "Psychological Problems",
  },
  {
    id: 2,
    name: "Cognitive Problems",
  },
];
export const preMedication = [
  {
    id: 1,
    name: "Prosthetic cardiac valves (transcatheter-implanted prostheses, homografts)",
  },
  {
    id: 2,
    name: "History of infective endocarditis",
  },
];
export const allergies = [
  {
    id: 1,
    name: "Penicillin / Amoxicillin",
  },
  {
    id: 2,
    name: "Sulfa Drugs",
  },
  {
    id: 3,
    name: "Azithromycin",
  },
  {
    id: 4,
    name: "Lidocaine",
  },
];
export const infectionDisease = [
  {
    id: 1,
    name: "HIV",
  },
  {
    id: 2,
    name: "Hepatitis B",
  },
  {
    id: 3,
    name: "Hepatitis C",
  },
  {
    id: 4,
    name: "Tuberculosis",
  },
];
export const dentalDisease = [
  {
    id: 1,
    name: "Swelling",
  },
  {
    id: 2,
    name: "Very Loose Teeth",
  },
  {
    id: 3,
    name: "Visible Infection",
  },
  {
    id: 4,
    name: "Extreme Fear of Dental Treatment",
  },
];
export const otherMedication = [
  {
    id: 1,
    name: "Aspirin",
  },
  {
    id: 2,
    name: "Ciprofloxacin",
  },
  {
    id: 3,
    name: "Fluoxetine",
  },
  {
    id: 4,
    name: "Losartan",
  },
];
