import AsyncStorage from '@react-native-async-storage/async-storage';
import {Storage, persistReducer, persistStore} from 'redux-persist';
import todoReducer from './reducer';
import {createStore} from 'redux';
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
};
const persistedReducer = persistReducer(persistConfig, todoReducer);

export const store = createStore(persistedReducer);
export const persistor = persistStore(store);
