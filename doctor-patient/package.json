{"name": "<PERSON>ientP<PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start --port 8090", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/blur": "^4.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "axios": "^1.7.9", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.77.0", "react-native-asset": "^2.1.1", "react-native-background-timer": "^2.4.1", "react-native-calendars": "^1.1309.1", "react-native-confirmation-code-field": "^7.4.0", "react-native-country-codes-picker": "^2.3.5", "react-native-date-picker": "^5.0.9", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "^2.23.0", "react-native-image-picker": "^7.2.3", "react-native-linear-gradient": "^2.8.3", "react-native-otp-entry": "^1.8.2", "react-native-permissions": "^5.2.4", "react-native-reanimated": "^3.16.7", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "^4.5.0", "react-native-simple-toast": "^3.3.2", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.3.0", "react-native-webview": "^13.13.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.0", "@react-native/eslint-config": "0.77.0", "@react-native/metro-config": "0.77.0", "@react-native/typescript-config": "0.77.0", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}