import { Github } from "@medusajs/icons"
import { But<PERSON>, Head<PERSON> } from "@medusajs/ui"

const Hero = () => {
  return (
    <div
      className="h-[75vh] w-full border-b border-ui-border-base relative bg-cover bg-center"
      style={{ backgroundImage: "url('/hero-image.jpg')" }}
    >
      <div className="absolute inset-0 z-10 flex flex-col justify-center items-center text-center small:p-32 gap-6">
        <span>
          <Heading
            level="h1"
            className="text-5xl leading-tight text-white font-heading"
          >
            Discover the Art of Luxury
          </Heading>
          <Heading
            level="h2"
            className="text-2xl leading-relaxed text-gray-200 font-sans"
          >
            Experience the epitome of style and sophistication with our exclusive collection.
          </Heading>
        </span>
        <Button variant="secondary" className="bg-gold-500 hover:bg-gold-400 text-white">
          Shop Now
        </Button>
      </div>
    </div>
  )
}

export default Hero
