// 1. Component Code
"use client";

import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

interface HeroProps {
  badge?: {
    text: string;
    action?: {
      text: string;
      href: string;
    };
  };
  title: string;
  subtitle?: string;
  description?: string;
  ctaButton?: {
    text: string;
    href: string;
  };
  secondaryButton?: {
    text: string;
    href: string;
  };
  image: {
    src: string;
    alt: string;
  };
}

export function LuxuryHero({
  badge,
  title = "Timeless Elegance, Modern Expression",
  subtitle,
  description = "Discover our curated collection of luxury garments crafted with exceptional materials and unparalleled attention to detail.",
  ctaButton = {
    text: "Shop Collection",
    href: "/collection",
  },
  secondaryButton = {
    text: "Explore",
    href: "/explore",
  },
  image = {
    src: "/luxury-fashion.jpg",
    alt: "Luxury clothing collection",
  },
}: HeroProps) {
  return (
    <section className="bg-background text-foreground py-16 md:py-24 lg:py-32 overflow-hidden">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col md:flex-row items-center gap-12 md:gap-16">
          <div className="flex flex-col gap-6 md:w-1/2">
            {badge && (
              <Badge variant="outline" className="w-fit">
                <span className="text-muted-foreground">{badge.text}</span>
                {badge.action && (
                  <a href={badge.action.href} className="ml-2 flex items-center gap-1">
                    {badge.action.text}
                    <ArrowRight className="h-3 w-3" />
                  </a>
                )}
              </Badge>
            )}
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-light tracking-tighter leading-tight">
              {title}
            </h1>
            
            {subtitle && (
              <h2 className="text-xl md:text-2xl font-light text-muted-foreground">
                {subtitle}
              </h2>
            )}
            
            {description && (
              <p className="text-base md:text-lg text-muted-foreground max-w-md">
                {description}
              </p>
            )}
            
            <div className="flex flex-col sm:flex-row gap-4 mt-2">
              {ctaButton && (
                <Button size="lg" asChild>
                  <a href={ctaButton.href}>{ctaButton.text}</a>
                </Button>
              )}
              
              {secondaryButton && (
                <Button size="lg" variant="outline" asChild>
                  <a href={secondaryButton.href} className="flex items-center gap-2">
                    {secondaryButton.text}
                    <ArrowRight className="h-4 w-4" />
                  </a>
                </Button>
              )}
            </div>
          </div>
          
          <div className="relative md:w-1/2 h-[400px] md:h-[500px] lg:h-[600px] w-full overflow-hidden rounded-md border border-border/10">
            <Image
              src={image.src}
              alt={image.alt}
              fill
              priority
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
