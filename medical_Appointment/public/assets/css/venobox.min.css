.vbox-overlay *,
.vbox-overlay :after,
.vbox-overlay :before {
    -webkit-backface-visibility: hidden;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

.vbox-overlay * {
    -webkit-backface-visibility: visible;
    backface-visibility: visible
}

.vbox-overlay {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 999999
}

.vbox-title {
    width: 100%;
    height: 40px;
    float: left;
    text-align: center;
    line-height: 28px;
    font-size: 12px;
    padding: 6px 50px;
    overflow: hidden;
    position: fixed;
    display: none;
    left: 0;
    z-index: 89
}

.vbox-close {
    cursor: pointer;
    position: fixed;
    top: -1px;
    right: 0;
    width: 50px;
    height: 40px;
    padding: 6px;
    display: block;
    background-position: 10px center;
    overflow: hidden;
    font-size: 24px;
    line-height: 1;
    text-align: center;
    z-index: 99
}

.vbox-left {
    cursor: pointer;
    position: fixed;
    left: 0;
    height: 40px;
    overflow: hidden;
    line-height: 28px;
    font-size: 12px;
    z-index: 99;
    display: flex;
    align-items: center
}

.vbox-num {
    display: inline-block;
    margin: 6px 0 6px 15px
}

.vbox-share {
    line-height: 28px;
    font-size: 12px;
    overflow: hidden;
    position: fixed;
    left: 0;
    z-index: 98;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    text-align: center
}

.vbox-share svg {
    max-height: 28px;
    width: 28px;
    z-index: 10;
    margin-left: 12px;
    margin-top: 6px;
    margin-bottom: 6px;
    vertical-align: middle
}

.vbox-next,
.vbox-prev {
    position: fixed;
    top: 50%;
    margin-top: -15px;
    overflow: hidden;
    cursor: pointer;
    display: block;
    width: 45px;
    height: 45px;
    z-index: 99
}

.vbox-next span,
.vbox-prev span {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top-color: #b6b6b6;
    border-right-color: #b6b6b6;
    text-indent: -100px;
    position: absolute;
    top: 8px;
    display: block
}

.vbox-prev {
    left: 15px
}

.vbox-next {
    right: 15px
}

.vbox-prev span {
    left: 10px;
    -ms-transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg);
    transform: rotate(-135deg)
}

.vbox-next span {
    -ms-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    right: 10px
}

.vbox-inline {
    width: 420px;
    height: 70vh;
    padding: 10px;
    background: #fff;
    margin: 0 auto;
    overflow: auto;
    text-align: left
}

.venoframe {
    max-width: 100%;
    border: none;
    width: 100%;
    height: 70vh
}

.venoframe.vbvid {
    height: 260px
}

@media (min-width: 768px) {

    .vbox-inline,
    .venoframe {
        width: 90%;
        height: 70vh
    }

    .venoframe.vbvid {
        width: 640px;
        height: 360px
    }
}

@media (min-width: 992px) {

    .vbox-inline,
    .venoframe {
        max-width: 1200px;
        width: 80%;
        height: 70vh
    }

    .venoframe.vbvid {
        width: 960px;
        height: 540px
    }
}

.vbox-open {
    overflow: hidden
}

.vbox-container {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    overflow-x: hidden;
    overflow-y: scroll;
    overflow-scrolling: touch;
    -webkit-overflow-scrolling: touch;
    z-index: 20;
    max-height: 100%
}

.vbox-content {
    text-align: center;
    width: auto;
    height: auto;
    position: relative;
    overflow: hidden;
    margin: 0 !important;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

.vbox-container img {
    width: auto !important;
    max-width: 100%;
    height: auto
}

.vbox-figlio {
    box-shadow: 0 0 12px rgba(0, 0, 0, .19), 0 6px 6px rgba(0, 0, 0, .23);
    max-width: 100%;
    text-align: initial
}

img.vbox-figlio {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none
}

.vbox-content.swipe-left {
    margin-left: -200px !important
}

.vbox-content.swipe-right {
    margin-left: 200px !important
}

.vbox-animated {
    webkit-transition: margin .3s ease-out;
    transition: margin .3s ease-out
}

.sk-double-bounce,
.sk-rotating-plane {
    width: 40px;
    height: 40px;
    margin: 40px auto
}

.sk-rotating-plane {
    background-color: #333;
    -webkit-animation: sk-rotatePlane 1.2s infinite ease-in-out;
    animation: sk-rotatePlane 1.2s infinite ease-in-out
}

@-webkit-keyframes sk-rotatePlane {
    0% {
        -webkit-transform: perspective(120px) rotateX(0) rotateY(0);
        transform: perspective(120px) rotateX(0) rotateY(0)
    }

    50% {
        -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0);
        transform: perspective(120px) rotateX(-180.1deg) rotateY(0)
    }

    100% {
        -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
        transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg)
    }
}

@keyframes sk-rotatePlane {
    0% {
        -webkit-transform: perspective(120px) rotateX(0) rotateY(0);
        transform: perspective(120px) rotateX(0) rotateY(0)
    }

    50% {
        -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0);
        transform: perspective(120px) rotateX(-180.1deg) rotateY(0)
    }

    100% {
        -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
        transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg)
    }
}

.sk-double-bounce {
    position: relative
}

.sk-double-bounce .sk-child {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #333;
    opacity: .6;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: sk-doubleBounce 2s infinite ease-in-out;
    animation: sk-doubleBounce 2s infinite ease-in-out
}

.sk-chasing-dots .sk-child,
.sk-spinner-pulse,
.sk-three-bounce .sk-child {
    background-color: #333;
    border-radius: 100%
}

.sk-double-bounce .sk-double-bounce2 {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

@-webkit-keyframes sk-doubleBounce {

    0%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes sk-doubleBounce {

    0%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.sk-wave {
    margin: 40px auto;
    width: 50px;
    height: 40px;
    text-align: center;
    font-size: 10px
}

.sk-wave .sk-rect {
    background-color: #333;
    height: 100%;
    width: 6px;
    display: inline-block;
    -webkit-animation: sk-waveStretchDelay 1.2s infinite ease-in-out;
    animation: sk-waveStretchDelay 1.2s infinite ease-in-out
}

.sk-wave .sk-rect1 {
    -webkit-animation-delay: -1.2s;
    animation-delay: -1.2s
}

.sk-wave .sk-rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.sk-wave .sk-rect3 {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.sk-wave .sk-rect4 {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.sk-wave .sk-rect5 {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}

@-webkit-keyframes sk-waveStretchDelay {

    0%,
    100%,
    40% {
        -webkit-transform: scaleY(.4);
        transform: scaleY(.4)
    }

    20% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

@keyframes sk-waveStretchDelay {

    0%,
    100%,
    40% {
        -webkit-transform: scaleY(.4);
        transform: scaleY(.4)
    }

    20% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1)
    }
}

.sk-wandering-cubes {
    margin: 40px auto;
    width: 40px;
    height: 40px;
    position: relative
}

.sk-wandering-cubes .sk-cube {
    background-color: #333;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: sk-wanderingCube 1.8s ease-in-out -1.8s infinite both;
    animation: sk-wanderingCube 1.8s ease-in-out -1.8s infinite both
}

.sk-chasing-dots,
.sk-spinner-pulse {
    width: 40px;
    height: 40px;
    margin: 40px auto
}

.sk-wandering-cubes .sk-cube2 {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

@-webkit-keyframes sk-wanderingCube {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    25% {
        -webkit-transform: translateX(30px) rotate(-90deg) scale(.5);
        transform: translateX(30px) rotate(-90deg) scale(.5)
    }

    50% {
        -webkit-transform: translateX(30px) translateY(30px) rotate(-179deg);
        transform: translateX(30px) translateY(30px) rotate(-179deg)
    }

    50.1% {
        -webkit-transform: translateX(30px) translateY(30px) rotate(-180deg);
        transform: translateX(30px) translateY(30px) rotate(-180deg)
    }

    75% {
        -webkit-transform: translateX(0) translateY(30px) rotate(-270deg) scale(.5);
        transform: translateX(0) translateY(30px) rotate(-270deg) scale(.5)
    }

    100% {
        -webkit-transform: rotate(-360deg);
        transform: rotate(-360deg)
    }
}

@keyframes sk-wanderingCube {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    25% {
        -webkit-transform: translateX(30px) rotate(-90deg) scale(.5);
        transform: translateX(30px) rotate(-90deg) scale(.5)
    }

    50% {
        -webkit-transform: translateX(30px) translateY(30px) rotate(-179deg);
        transform: translateX(30px) translateY(30px) rotate(-179deg)
    }

    50.1% {
        -webkit-transform: translateX(30px) translateY(30px) rotate(-180deg);
        transform: translateX(30px) translateY(30px) rotate(-180deg)
    }

    75% {
        -webkit-transform: translateX(0) translateY(30px) rotate(-270deg) scale(.5);
        transform: translateX(0) translateY(30px) rotate(-270deg) scale(.5)
    }

    100% {
        -webkit-transform: rotate(-360deg);
        transform: rotate(-360deg)
    }
}

.sk-spinner-pulse {
    -webkit-animation: sk-pulseScaleOut 1s infinite ease-in-out;
    animation: sk-pulseScaleOut 1s infinite ease-in-out
}

@-webkit-keyframes sk-pulseScaleOut {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 0
    }
}

@keyframes sk-pulseScaleOut {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 0
    }
}

.sk-chasing-dots {
    position: relative;
    text-align: center;
    -webkit-animation: sk-chasingDotsRotate 2s infinite linear;
    animation: sk-chasingDotsRotate 2s infinite linear
}

.sk-chasing-dots .sk-child {
    width: 60%;
    height: 60%;
    display: inline-block;
    position: absolute;
    top: 0;
    -webkit-animation: sk-chasingDotsBounce 2s infinite ease-in-out;
    animation: sk-chasingDotsBounce 2s infinite ease-in-out
}

.sk-chasing-dots .sk-dot2 {
    top: auto;
    bottom: 0;
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

@-webkit-keyframes sk-chasingDotsRotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes sk-chasingDotsRotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes sk-chasingDotsBounce {

    0%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes sk-chasingDotsBounce {

    0%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.sk-three-bounce {
    margin: 40px auto;
    width: 80px;
    text-align: center
}

.sk-three-bounce .sk-child {
    width: 20px;
    height: 20px;
    display: inline-block;
    -webkit-animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
    animation: sk-three-bounce 1.4s ease-in-out 0s infinite both
}

.sk-circle .sk-child:before,
.sk-fading-circle .sk-circle:before {
    display: block;
    border-radius: 100%;
    content: '';
    background-color: #333
}

.sk-three-bounce .sk-bounce1 {
    -webkit-animation-delay: -.32s;
    animation-delay: -.32s
}

.sk-three-bounce .sk-bounce2 {
    -webkit-animation-delay: -.16s;
    animation-delay: -.16s
}

@-webkit-keyframes sk-three-bounce {

    0%,
    100%,
    80% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    40% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes sk-three-bounce {

    0%,
    100%,
    80% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    40% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.sk-circle {
    margin: 40px auto;
    width: 40px;
    height: 40px;
    position: relative
}

.sk-circle .sk-child {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0
}

.sk-circle .sk-child:before {
    margin: 0 auto;
    width: 15%;
    height: 15%;
    -webkit-animation: sk-circleBounceDelay 1.2s infinite ease-in-out both;
    animation: sk-circleBounceDelay 1.2s infinite ease-in-out both
}

.sk-circle .sk-circle2 {
    -webkit-transform: rotate(30deg);
    -ms-transform: rotate(30deg);
    transform: rotate(30deg)
}

.sk-circle .sk-circle3 {
    -webkit-transform: rotate(60deg);
    -ms-transform: rotate(60deg);
    transform: rotate(60deg)
}

.sk-circle .sk-circle4 {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.sk-circle .sk-circle5 {
    -webkit-transform: rotate(120deg);
    -ms-transform: rotate(120deg);
    transform: rotate(120deg)
}

.sk-circle .sk-circle6 {
    -webkit-transform: rotate(150deg);
    -ms-transform: rotate(150deg);
    transform: rotate(150deg)
}

.sk-circle .sk-circle7 {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}

.sk-circle .sk-circle8 {
    -webkit-transform: rotate(210deg);
    -ms-transform: rotate(210deg);
    transform: rotate(210deg)
}

.sk-circle .sk-circle9 {
    -webkit-transform: rotate(240deg);
    -ms-transform: rotate(240deg);
    transform: rotate(240deg)
}

.sk-circle .sk-circle10 {
    -webkit-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg)
}

.sk-circle .sk-circle11 {
    -webkit-transform: rotate(300deg);
    -ms-transform: rotate(300deg);
    transform: rotate(300deg)
}

.sk-circle .sk-circle12 {
    -webkit-transform: rotate(330deg);
    -ms-transform: rotate(330deg);
    transform: rotate(330deg)
}

.sk-circle .sk-circle2:before {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.sk-circle .sk-circle3:before {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.sk-circle .sk-circle4:before {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.sk-circle .sk-circle5:before {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}

.sk-circle .sk-circle6:before {
    -webkit-animation-delay: -.7s;
    animation-delay: -.7s
}

.sk-circle .sk-circle7:before {
    -webkit-animation-delay: -.6s;
    animation-delay: -.6s
}

.sk-circle .sk-circle8:before {
    -webkit-animation-delay: -.5s;
    animation-delay: -.5s
}

.sk-circle .sk-circle9:before {
    -webkit-animation-delay: -.4s;
    animation-delay: -.4s
}

.sk-circle .sk-circle10:before {
    -webkit-animation-delay: -.3s;
    animation-delay: -.3s
}

.sk-circle .sk-circle11:before {
    -webkit-animation-delay: -.2s;
    animation-delay: -.2s
}

.sk-circle .sk-circle12:before {
    -webkit-animation-delay: -.1s;
    animation-delay: -.1s
}

@-webkit-keyframes sk-circleBounceDelay {

    0%,
    100%,
    80% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    40% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes sk-circleBounceDelay {

    0%,
    100%,
    80% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    40% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.sk-cube-grid {
    width: 40px;
    height: 40px;
    margin: 40px auto
}

.sk-cube-grid .sk-cube {
    width: 33.33%;
    height: 33.33%;
    background-color: #333;
    float: left;
    -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
    animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out
}

.sk-cube-grid .sk-cube1 {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.sk-cube-grid .sk-cube2 {
    -webkit-animation-delay: .3s;
    animation-delay: .3s
}

.sk-cube-grid .sk-cube3 {
    -webkit-animation-delay: .4s;
    animation-delay: .4s
}

.sk-cube-grid .sk-cube4 {
    -webkit-animation-delay: .1s;
    animation-delay: .1s
}

.sk-cube-grid .sk-cube5 {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.sk-cube-grid .sk-cube6 {
    -webkit-animation-delay: .3s;
    animation-delay: .3s
}

.sk-cube-grid .sk-cube7 {
    -webkit-animation-delay: 0s;
    animation-delay: 0s
}

.sk-cube-grid .sk-cube8 {
    -webkit-animation-delay: .1s;
    animation-delay: .1s
}

.sk-cube-grid .sk-cube9 {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

@-webkit-keyframes sk-cubeGridScaleDelay {

    0%,
    100%,
    70% {
        -webkit-transform: scale3D(1, 1, 1);
        transform: scale3D(1, 1, 1)
    }

    35% {
        -webkit-transform: scale3D(0, 0, 1);
        transform: scale3D(0, 0, 1)
    }
}

@keyframes sk-cubeGridScaleDelay {

    0%,
    100%,
    70% {
        -webkit-transform: scale3D(1, 1, 1);
        transform: scale3D(1, 1, 1)
    }

    35% {
        -webkit-transform: scale3D(0, 0, 1);
        transform: scale3D(0, 0, 1)
    }
}

.sk-fading-circle {
    margin: 40px auto;
    width: 40px;
    height: 40px;
    position: relative
}

.sk-fading-circle .sk-circle {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0
}

.sk-fading-circle .sk-circle:before {
    margin: 0 auto;
    width: 15%;
    height: 15%;
    -webkit-animation: sk-circleFadeDelay 1.2s infinite ease-in-out both;
    animation: sk-circleFadeDelay 1.2s infinite ease-in-out both
}

.sk-fading-circle .sk-circle2 {
    -webkit-transform: rotate(30deg);
    -ms-transform: rotate(30deg);
    transform: rotate(30deg)
}

.sk-fading-circle .sk-circle3 {
    -webkit-transform: rotate(60deg);
    -ms-transform: rotate(60deg);
    transform: rotate(60deg)
}

.sk-fading-circle .sk-circle4 {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.sk-fading-circle .sk-circle5 {
    -webkit-transform: rotate(120deg);
    -ms-transform: rotate(120deg);
    transform: rotate(120deg)
}

.sk-fading-circle .sk-circle6 {
    -webkit-transform: rotate(150deg);
    -ms-transform: rotate(150deg);
    transform: rotate(150deg)
}

.sk-fading-circle .sk-circle7 {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}

.sk-fading-circle .sk-circle8 {
    -webkit-transform: rotate(210deg);
    -ms-transform: rotate(210deg);
    transform: rotate(210deg)
}

.sk-fading-circle .sk-circle9 {
    -webkit-transform: rotate(240deg);
    -ms-transform: rotate(240deg);
    transform: rotate(240deg)
}

.sk-fading-circle .sk-circle10 {
    -webkit-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg)
}

.sk-fading-circle .sk-circle11 {
    -webkit-transform: rotate(300deg);
    -ms-transform: rotate(300deg);
    transform: rotate(300deg)
}

.sk-fading-circle .sk-circle12 {
    -webkit-transform: rotate(330deg);
    -ms-transform: rotate(330deg);
    transform: rotate(330deg)
}

.sk-fading-circle .sk-circle2:before {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.sk-fading-circle .sk-circle3:before {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.sk-fading-circle .sk-circle4:before {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.sk-fading-circle .sk-circle5:before {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}

.sk-fading-circle .sk-circle6:before {
    -webkit-animation-delay: -.7s;
    animation-delay: -.7s
}

.sk-fading-circle .sk-circle7:before {
    -webkit-animation-delay: -.6s;
    animation-delay: -.6s
}

.sk-fading-circle .sk-circle8:before {
    -webkit-animation-delay: -.5s;
    animation-delay: -.5s
}

.sk-fading-circle .sk-circle9:before {
    -webkit-animation-delay: -.4s;
    animation-delay: -.4s
}

.sk-fading-circle .sk-circle10:before {
    -webkit-animation-delay: -.3s;
    animation-delay: -.3s
}

.sk-fading-circle .sk-circle11:before {
    -webkit-animation-delay: -.2s;
    animation-delay: -.2s
}

.sk-fading-circle .sk-circle12:before {
    -webkit-animation-delay: -.1s;
    animation-delay: -.1s
}

@-webkit-keyframes sk-circleFadeDelay {

    0%,
    100%,
    39% {
        opacity: 0
    }

    40% {
        opacity: 1
    }
}

@keyframes sk-circleFadeDelay {

    0%,
    100%,
    39% {
        opacity: 0
    }

    40% {
        opacity: 1
    }
}

.sk-folding-cube {
    margin: 40px auto;
    width: 40px;
    height: 40px;
    position: relative;
    -webkit-transform: rotateZ(45deg);
    transform: rotateZ(45deg)
}

.sk-folding-cube .sk-cube {
    float: left;
    width: 50%;
    height: 50%;
    position: relative;
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.sk-folding-cube .sk-cube:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #333;
    -webkit-animation: sk-foldCubeAngle 2.4s infinite linear both;
    animation: sk-foldCubeAngle 2.4s infinite linear both;
    -webkit-transform-origin: 100% 100%;
    -ms-transform-origin: 100% 100%;
    transform-origin: 100% 100%
}

.sk-folding-cube .sk-cube2 {
    -webkit-transform: scale(1.1) rotateZ(90deg);
    transform: scale(1.1) rotateZ(90deg)
}

.sk-folding-cube .sk-cube3 {
    -webkit-transform: scale(1.1) rotateZ(180deg);
    transform: scale(1.1) rotateZ(180deg)
}

.sk-folding-cube .sk-cube4 {
    -webkit-transform: scale(1.1) rotateZ(270deg);
    transform: scale(1.1) rotateZ(270deg)
}

.sk-folding-cube .sk-cube2:before {
    -webkit-animation-delay: .3s;
    animation-delay: .3s
}

.sk-folding-cube .sk-cube3:before {
    -webkit-animation-delay: .6s;
    animation-delay: .6s
}

.sk-folding-cube .sk-cube4:before {
    -webkit-animation-delay: .9s;
    animation-delay: .9s
}

@-webkit-keyframes sk-foldCubeAngle {

    0%,
    10% {
        -webkit-transform: perspective(140px) rotateX(-180deg);
        transform: perspective(140px) rotateX(-180deg);
        opacity: 0
    }

    25%,
    75% {
        -webkit-transform: perspective(140px) rotateX(0);
        transform: perspective(140px) rotateX(0);
        opacity: 1
    }

    100%,
    90% {
        -webkit-transform: perspective(140px) rotateY(180deg);
        transform: perspective(140px) rotateY(180deg);
        opacity: 0
    }
}

@keyframes sk-foldCubeAngle {

    0%,
    10% {
        -webkit-transform: perspective(140px) rotateX(-180deg);
        transform: perspective(140px) rotateX(-180deg);
        opacity: 0
    }

    25%,
    75% {
        -webkit-transform: perspective(140px) rotateX(0);
        transform: perspective(140px) rotateX(0);
        opacity: 1
    }

    100%,
    90% {
        -webkit-transform: perspective(140px) rotateY(180deg);
        transform: perspective(140px) rotateY(180deg);
        opacity: 0
    }
}