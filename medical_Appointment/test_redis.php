<?php

require 'vendor/autoload.php';

use Predis\Client;

try {
    echo "Connecting to Redis/Valkey...\n";

    // Create Redis client with SSL options
    $client = new Client([
        'scheme' => 'tls',
        'host' => 'xtasker-chukwurahdavid-624e.l.aivencloud.com',
        'port' => 22787,
        'password' => 'AVNS_hLiS5UziTpKHl2NS5ey',
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false,
        ]
    ]);
    
    // Test connection
    $client->ping();
    echo "✅ Successfully connected to Redis/Valkey!\n";
    
    // Test basic operations
    $client->set('test_key', 'Hello from Dr. Fintan Medical App!');
    $value = $client->get('test_key');
    
    echo "✅ Set and retrieved value: {$value}\n";
    
    // Test with expiration
    $client->setex('temp_key', 60, 'This will expire in 60 seconds');
    $temp_value = $client->get('temp_key');
    echo "✅ Set temporary value: {$temp_value}\n";
    
    // Test hash operations
    $client->hset('user:1', 'name', 'Dr. Fintan');
    $client->hset('user:1', 'role', 'doctor');
    $client->hset('user:1', 'department', 'Neurology');
    
    $user_data = $client->hgetall('user:1');
    echo "✅ Hash data stored:\n";
    foreach ($user_data as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    
    // Test list operations
    $client->lpush('appointments', 'Patient A - 10:00 AM');
    $client->lpush('appointments', 'Patient B - 11:00 AM');
    $client->lpush('appointments', 'Patient C - 2:00 PM');
    
    $appointments = $client->lrange('appointments', 0, -1);
    echo "✅ Appointments list:\n";
    foreach ($appointments as $appointment) {
        echo "   - {$appointment}\n";
    }
    
    // Clean up test data
    $client->del(['test_key', 'temp_key', 'user:1', 'appointments']);
    echo "✅ Test data cleaned up\n";
    
    echo "\n🎉 Redis/Valkey is working perfectly with the Dr. Fintan Medical App!\n";
    
} catch (Exception $e) {
    echo "❌ Error connecting to Redis/Valkey: " . $e->getMessage() . "\n";
    echo "Please check your connection details and try again.\n";
}
