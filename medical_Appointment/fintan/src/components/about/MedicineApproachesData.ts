
import { MedicineApproach } from './ApproachesSection';

export const medicineApproaches: MedicineApproach[] = [
  {
    title: "Conventional Medicine",
    points: [
      "Disease focused",
      "Treats symptoms using drugs, radiation or surgery",
      "Serves as knowledge base for Western medicine"
    ]
  },
  {
    title: "Alternative Medicine",
    points: [
      "Used in place of conventional medicine",
      "Treats symptoms using methods that are \"closer to nature\"",
      "Not all treatments are scientifically validated"
    ]
  },
  {
    title: "Complementary Medicine",
    points: [
      "Modern approach that uses both conventional & alternative medicines",
      "Diagnoses and treats symptoms with hybrid methods",
      "Combines best practices from multiple medical disciplines"
    ]
  },
  {
    title: "Integrative Medicine",
    points: [
      "Combines conventional and alternative medicines in a coordinated way",
      "Emphasis on data driven solutions",
      "Patient-focused and safety-oriented approach"
    ]
  },
  {
    title: "Functional Medicine",
    points: [
      "Questions the foundations of conventional medicine",
      "Treats the patient, not the disease",
      "Scientifically based in systems biology with focus on prevention"
    ]
  }
];
