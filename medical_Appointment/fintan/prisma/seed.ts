import { PrismaClient, UserRole, ConsultationType, AppointmentStatus, ConsultationStatus, PaymentMethod, PaymentStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

async function main() {
  console.log('Starting seed...');

  // Clear existing data
  await prisma.prescription.deleteMany({});
  await prisma.medicalRecord.deleteMany({});
  await prisma.payment.deleteMany({});
  await prisma.consultation.deleteMany({});
  await prisma.appointment.deleteMany({});
  await prisma.availability.deleteMany({});
  await prisma.provider.deleteMany({});
  await prisma.patient.deleteMany({});
  await prisma.user.deleteMany({});

  console.log('Cleared existing data');

  // Create admin user
  const adminPassword = await hashPassword('admin123');
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Admin User',
      role: UserRole.ADMIN,
    },
  });
  console.log('Created admin user');

  // Create providers (doctors)
  const providers = [];
  const providerData = [
    {
      email: '<EMAIL>',
      password: 'doctor123',
      name: 'Dr. John Smith',
      phone: '(*************',
      title: 'Dr.',
      specialization: 'General Medicine',
      bio: 'Board-certified physician with over 10 years of experience in general medicine and primary care.',
      licenseNumber: 'MD12345',
    },
    {
      email: '<EMAIL>',
      password: 'doctor123',
      name: 'Dr. Priya Patel',
      phone: '(*************',
      title: 'Dr.',
      specialization: 'Pediatrics',
      bio: 'Pediatrician with a focus on child development and preventive care.',
      licenseNumber: 'MD23456',
    },
    {
      email: '<EMAIL>',
      password: 'doctor123',
      name: 'Dr. Robert Johnson',
      phone: '(*************',
      title: 'Dr.',
      specialization: 'Dermatology',
      bio: 'Dermatologist specializing in skin conditions and cosmetic procedures.',
      licenseNumber: 'MD34567',
    },
  ];

  for (const data of providerData) {
    const hashedPassword = await hashPassword(data.password);
    const user = await prisma.user.create({
      data: {
        email: data.email,
        password: hashedPassword,
        name: data.name,
        phone: data.phone,
        role: UserRole.PROVIDER,
      },
    });

    const provider = await prisma.provider.create({
      data: {
        userId: user.id,
        title: data.title,
        specialization: data.specialization,
        bio: data.bio,
        licenseNumber: data.licenseNumber,
      },
    });

    providers.push({ user, provider });
    console.log(`Created provider: ${data.name}`);

    // Create availability for each provider
    const daysOfWeek = [0, 1, 2, 3, 4]; // Sunday to Thursday
    for (const day of daysOfWeek) {
      await prisma.availability.create({
        data: {
          providerId: provider.id,
          dayOfWeek: day,
          startTime: '09:00',
          endTime: '17:00',
          isAvailable: true,
        },
      });
    }
    console.log(`Created availability for: ${data.name}`);
  }

  // Create patients
  const patients = [];
  const patientData = [
    {
      email: '<EMAIL>',
      password: 'patient123',
      name: 'John Doe',
      phone: '(*************',
      dateOfBirth: new Date('1985-05-15'),
      address: '123 Main St, Anytown, USA',
      emergencyContact: 'Jane Doe (Wife) - (*************',
    },
    {
      email: '<EMAIL>',
      password: 'patient123',
      name: 'Sarah Jones',
      phone: '(*************',
      dateOfBirth: new Date('1990-08-21'),
      address: '456 Oak Ave, Somewhere, USA',
      emergencyContact: 'Mike Jones (Husband) - (*************',
    },
    {
      email: '<EMAIL>',
      password: 'patient123',
      name: 'Michael Brown',
      phone: '(*************',
      dateOfBirth: new Date('1978-12-03'),
      address: '789 Pine St, Nowhere, USA',
      emergencyContact: 'Lisa Brown (Sister) - (*************',
    },
    {
      email: '<EMAIL>',
      password: 'patient123',
      name: 'Emily Williams',
      phone: '(*************',
      dateOfBirth: new Date('1995-03-27'),
      address: '321 Elm St, Anyplace, USA',
      emergencyContact: 'Robert Williams (Father) - (*************',
    },
  ];

  for (const data of patientData) {
    const hashedPassword = await hashPassword(data.password);
    const user = await prisma.user.create({
      data: {
        email: data.email,
        password: hashedPassword,
        name: data.name,
        phone: data.phone,
        role: UserRole.PATIENT,
      },
    });

    const patient = await prisma.patient.create({
      data: {
        userId: user.id,
        dateOfBirth: data.dateOfBirth,
        address: data.address,
        emergencyContact: data.emergencyContact,
      },
    });

    patients.push({ user, patient });
    console.log(`Created patient: ${data.name}`);

    // Create medical records for each patient
    const medicalRecordData = [
      {
        title: 'Annual Physical',
        description: 'Routine annual physical examination. All vitals normal.',
        recordType: 'Examination',
        recordDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
      },
      {
        title: 'Allergy Diagnosis',
        description: 'Patient diagnosed with seasonal allergies. Prescribed antihistamines.',
        recordType: 'Diagnosis',
        recordDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // 180 days ago
      },
    ];

    for (const record of medicalRecordData) {
      await prisma.medicalRecord.create({
        data: {
          patientId: patient.id,
          title: record.title,
          description: record.description,
          recordType: record.recordType,
          recordDate: record.recordDate,
        },
      });
    }
    console.log(`Created medical records for: ${data.name}`);
  }

  // Create appointments, consultations, and payments
  const now = new Date();
  const appointmentData = [
    {
      patientIndex: 0,
      providerIndex: 0,
      consultationType: ConsultationType.VIDEO,
      appointmentDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 10, 0), // Tomorrow at 10:00 AM
      reason: 'Annual check-up and prescription renewal',
      status: AppointmentStatus.CONFIRMED,
      consultationStatus: ConsultationStatus.SCHEDULED,
      paymentStatus: PaymentStatus.COMPLETED,
      amount: 75.00,
    },
    {
      patientIndex: 1,
      providerIndex: 1,
      consultationType: ConsultationType.AUDIO,
      appointmentDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 14, 30), // Tomorrow at 2:30 PM
      reason: 'Follow-up on recent lab results',
      status: AppointmentStatus.CONFIRMED,
      consultationStatus: ConsultationStatus.SCHEDULED,
      paymentStatus: PaymentStatus.COMPLETED,
      amount: 50.00,
    },
    {
      patientIndex: 2,
      providerIndex: 2,
      consultationType: ConsultationType.VIDEO,
      appointmentDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 2, 11, 15), // Day after tomorrow at 11:15 AM
      reason: 'Skin rash consultation',
      status: AppointmentStatus.SCHEDULED,
      consultationStatus: ConsultationStatus.SCHEDULED,
      paymentStatus: PaymentStatus.PENDING,
      amount: 75.00,
    },
    {
      patientIndex: 3,
      providerIndex: 0,
      consultationType: ConsultationType.AUDIO,
      appointmentDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3, 9, 0), // 3 days from now at 9:00 AM
      reason: 'Headache and dizziness follow-up',
      status: AppointmentStatus.SCHEDULED,
      consultationStatus: ConsultationStatus.SCHEDULED,
      paymentStatus: PaymentStatus.PENDING,
      amount: 50.00,
    },
    {
      patientIndex: 0,
      providerIndex: 1,
      consultationType: ConsultationType.VIDEO,
      appointmentDate: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7, 13, 0), // 7 days ago at 1:00 PM
      reason: 'Flu symptoms and fever',
      status: AppointmentStatus.COMPLETED,
      consultationStatus: ConsultationStatus.COMPLETED,
      paymentStatus: PaymentStatus.COMPLETED,
      amount: 75.00,
      notes: 'Patient presented with flu-like symptoms. Prescribed rest, fluids, and over-the-counter pain relievers.',
      prescriptions: [
        {
          medication: 'Acetaminophen',
          dosage: '500mg',
          frequency: 'Every 6 hours as needed',
          duration: '5 days',
          instructions: 'Take with food or water for pain and fever',
        },
      ],
    },
  ];

  for (const data of appointmentData) {
    const patient = patients[data.patientIndex];
    const provider = providers[data.providerIndex];

    // Create appointment
    const appointment = await prisma.appointment.create({
      data: {
        patientId: patient.patient.id,
        providerId: provider.provider.id,
        createdById: patient.user.id,
        consultationType: data.consultationType,
        appointmentDate: data.appointmentDate,
        reason: data.reason,
        status: data.status,
      },
    });

    // Create consultation
    const sessionId = `session_${appointment.id}_${Date.now()}`;
    const roomUrl = `https://virtualcare.daily.co/${appointment.id}`;
    
    const consultation = await prisma.consultation.create({
      data: {
        appointmentId: appointment.id,
        sessionId: sessionId,
        roomUrl: roomUrl,
        status: data.consultationStatus,
        notes: data.notes || null,
        startTime: data.status === AppointmentStatus.COMPLETED ? new Date(data.appointmentDate.getTime() - 30 * 60 * 1000) : null,
        endTime: data.status === AppointmentStatus.COMPLETED ? data.appointmentDate : null,
      },
    });

    // Create payment
    await prisma.payment.create({
      data: {
        appointmentId: appointment.id,
        amount: data.amount,
        paymentMethod: PaymentMethod.STRIPE,
        transactionId: data.paymentStatus === PaymentStatus.COMPLETED ? `tx_${Date.now()}` : null,
        status: data.paymentStatus,
      },
    });

    console.log(`Created appointment, consultation, and payment for ${patient.user.name} with ${provider.user.name}`);

    // Create prescriptions if any
    if (data.prescriptions) {
      for (const prescriptionData of data.prescriptions) {
        await prisma.prescription.create({
          data: {
            consultationId: consultation.id,
            ...prescriptionData,
          },
        });
      }
      console.log(`Created prescriptions for consultation ${consultation.id}`);
    }
  }

  console.log('Seed completed successfully');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

