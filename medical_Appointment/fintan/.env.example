# Neon PostgreSQL Connection
# IMPORTANT: Replace these placeholder values with your actual credentials

# Development Database URLs
DATABASE_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?pgbouncer=true&connect_timeout=10"
DIRECT_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?connect_timeout=10"

# Production Database URLs (for Render, Vercel, etc.)
# DATABASE_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?pgbouncer=true&connect_timeout=10"
# DIRECT_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?connect_timeout=10"

# IMPORTANT: For Neon Serverless PostgreSQL
# - DATABASE_URL should use pgbouncer=true for connection pooling
# - DIRECT_URL should connect directly without pgbouncer for migrations
# - Both are required for Prisma to work correctly with Neon

# Application Settings
NODE_ENV="development" # Use "production" for deployment
PORT=3000

# URLs - IMPORTANT: Change these for production!
# Development URLs
API_BASE_URL="http://localhost:3000/api"
FRONTEND_URL="http://localhost:3000"
VITE_APP_URL="http://localhost:8080"
VITE_API_URL="http://localhost:3001"

# Production URLs (uncomment and update for deployment)
# API_BASE_URL="https://[YOUR_PRODUCTION_DOMAIN]/api"
# FRONTEND_URL="https://[YOUR_PRODUCTION_DOMAIN]"

# Authentication
JWT_SECRET="[YOUR_JWT_SECRET_KEY]"
JWT_EXPIRES_IN="7d" # 7 days
REFRESH_TOKEN_SECRET="[YOUR_REFRESH_TOKEN_SECRET]"
REFRESH_TOKEN_EXPIRES_IN="30d" # 30 days
SESSION_SECRET="[YOUR_SESSION_SECRET]"

# Daily.co Video/Audio API
DAILY_API_KEY="[YOUR_DAILY_API_KEY]"
DAILY_API_URL="https://api.daily.co/v1"
VITE_DAILY_API_KEY="[YOUR_DAILY_API_KEY]"

# OAuth/SSO Configuration
# Google OAuth
VITE_GOOGLE_CLIENT_ID="[YOUR_GOOGLE_CLIENT_ID]"
GOOGLE_CLIENT_SECRET="[YOUR_GOOGLE_CLIENT_SECRET]"

# Microsoft OAuth
VITE_MICROSOFT_CLIENT_ID="[YOUR_MICROSOFT_CLIENT_ID]"
MICROSOFT_CLIENT_SECRET="[YOUR_MICROSOFT_CLIENT_SECRET]"

# Apple OAuth
VITE_APPLE_CLIENT_ID="[YOUR_APPLE_CLIENT_ID]"
APPLE_CLIENT_SECRET="[YOUR_APPLE_CLIENT_SECRET]"

# Email Service (for password reset, notifications)
EMAIL_SERVICE="smtp" # smtp, sendgrid, mailgun, etc.
EMAIL_HOST="[YOUR_SMTP_HOST]"
EMAIL_PORT=587
EMAIL_USER="[YOUR_EMAIL]"
EMAIL_PASSWORD="[YOUR_EMAIL_PASSWORD]"
EMAIL_FROM="Fintan Virtual Care <[YOUR_FROM_EMAIL]>"

# SMTP Configuration (alternative format)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="[<EMAIL>]"
SMTP_PASS="[YOUR_APP_PASSWORD]"

# Payment Processing
STRIPE_SECRET_KEY="[YOUR_STRIPE_SECRET_KEY]"
STRIPE_PUBLISHABLE_KEY="[YOUR_STRIPE_PUBLISHABLE_KEY]"
STRIPE_WEBHOOK_SECRET="[YOUR_STRIPE_WEBHOOK_SECRET]"
PAYSTACK_PUBLIC_KEY="[YOUR_PAYSTACK_PUBLIC_KEY]"
PAYSTACK_SECRET_KEY="[YOUR_PAYSTACK_SECRET_KEY]"

# Notification Service
NOTIFICATION_API_CLIENT_ID="[YOUR_NOTIFICATION_API_CLIENT_ID]"
NOTIFICATION_API_CLIENT_SECRET="[YOUR_NOTIFICATION_API_CLIENT_SECRET]"

# Security and Rate Limiting
# For development (localhost only)
CORS_ORIGIN="http://localhost:3000,http://localhost:8080"

# For production (update with your domains)
# CORS_ORIGIN="https://[YOUR_PRODUCTION_DOMAIN],https://admin.[YOUR_PRODUCTION_DOMAIN]"

RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100 # Maximum requests per window

# Logging
LOG_LEVEL="info" # debug, info, warn, error

# Feature Flags
ENABLE_VIDEO_CALLS=true
ENABLE_AUDIO_CALLS=true
ENABLE_PAYMENTS=true
ENABLE_NOTIFICATIONS=true

# Deployment Settings
# Set to true to run migrations during build (for platforms like Render)
RUN_MIGRATIONS_ON_BUILD=true

