<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;

class Doctor<PERSON>eeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        // Clear existing data (PostgreSQL compatible)
        DB::table('doctors')->delete();
        DB::table('users')->where('role', 'doctor')->delete();

        // Create Dr. <PERSON><PERSON> - the only doctor
        $email = '<EMAIL>';

        // First create the user account for Dr. <PERSON><PERSON>
        $userId = DB::table('users')->insertGetId([
            'email' => $email,
            'password' => Hash::make('fintan123'), // Default password
            'role' => 'doctor',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Then create Dr. <PERSON><PERSON>'s doctor record
        DB::table('doctors')->insert([
            'user_id' => $userId,
            'name' => 'Dr. <PERSON><PERSON>',
            'mobile' => '+****************',
            'email' => $email,
            'department' => 'Integrative Medicine & Neurology',
            'availability' => 'available',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}