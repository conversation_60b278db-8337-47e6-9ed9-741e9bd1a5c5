<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;

class Doctor<PERSON>eed<PERSON> extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $faker = Faker::create();

        $departments = [
            'Cardiology',
            'Ophthalmology',
            'Pediatrics',
            'Radiology',
            'Urology',
            'Neurology',
            'Orthopedics',
            'Dermatology',
            'Gastroenterology',
            'Endocrinology',
            'Oncology',
            'Psychiatry',
            'Nephrology',
            'Pulmonology',
            'Rheumatology'
        ];

        // Clear existing data (SQLite compatible)
        DB::table('doctors')->delete();
        DB::table('users')->where('role', 'doctor')->delete();

        for ($i = 0; $i < 5; $i++) {
            $email = 'doctor' . ($i + 1) . '@medical.com';

            // First create the user account
            $userId = DB::table('users')->insertGetId([
                'email' => $email,
                'password' => Hash::make('doctor123'), // Default password
                'role' => 'doctor',
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Then create the doctor record
            DB::table('doctors')->insert([
                'user_id' => $userId,
                'name' => 'Dr. ' . $faker->name,
                'mobile' => $faker->phoneNumber,
                'email' => $email,
                'department' => $departments[$i % count($departments)],
                'availability' => 'available',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}