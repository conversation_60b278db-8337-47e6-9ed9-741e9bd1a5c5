@include('layouts.header')

@include('layouts.navbar')

<!-- Modern Booking Header -->
<section class="py-12 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px]"></div>
    
    <div class="container mx-auto px-4 relative">
        <div class="text-center max-w-4xl mx-auto">
            <div class="mb-6">
                <div class="inline-flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-4">
                    <i class="fas fa-calendar-check text-white text-xl"></i>
                    <span class="text-white font-medium">Book Consultation</span>
                </div>
            </div>
            
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
                Schedule Your 
                <span class="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                    Consultation
                </span>
            </h1>
            
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
                Choose the perfect time that works for you with our intelligent scheduling system. 
                Experience personalized healthcare from the comfort of your home.
            </p>
            
            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <div class="text-3xl font-bold text-white mb-2">15min</div>
                    <div class="text-blue-100 text-sm">Average Booking Time</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <div class="text-3xl font-bold text-white mb-2">24/7</div>
                    <div class="text-blue-100 text-sm">Available Scheduling</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <div class="text-3xl font-bold text-white mb-2">98%</div>
                    <div class="text-blue-100 text-sm">Patient Satisfaction</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Multi-Step Booking -->
<section class="py-16 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 min-h-screen">
    <div class="container mx-auto max-w-5xl px-4">
        <!-- Booking Progress -->
        <div class="mb-12">
            <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl border-0 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        Booking Progress
                    </h2>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        Step <span id="current-step">1</span> of 4
                    </span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Step 1: Doctor Selection -->
                    <div class="flex items-center">
                        <div class="step-indicator w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold" data-step="1">
                            1
                        </div>
                        <span class="ml-3 text-sm font-medium text-blue-600 dark:text-blue-400">Doctor</span>
                    </div>
                    
                    <div class="flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded step-line" data-step="1"></div>
                    
                    <!-- Step 2: Date & Time -->
                    <div class="flex items-center">
                        <div class="step-indicator w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold" data-step="2">
                            2
                        </div>
                        <span class="ml-3 text-sm font-medium text-gray-500">Date & Time</span>
                    </div>
                    
                    <div class="flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded step-line" data-step="2"></div>
                    
                    <!-- Step 3: Your Info -->
                    <div class="flex items-center">
                        <div class="step-indicator w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold" data-step="3">
                            3
                        </div>
                        <span class="ml-3 text-sm font-medium text-gray-500">Your Info</span>
                    </div>
                    
                    <div class="flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded step-line" data-step="3"></div>
                    
                    <!-- Step 4: Confirm -->
                    <div class="flex items-center">
                        <div class="step-indicator w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold" data-step="4">
                            4
                        </div>
                        <span class="ml-3 text-sm font-medium text-gray-500">Confirm</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Form Container -->
        <div class="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-2xl shadow-2xl border-0">
            <form id="appointmentForm" class="p-8">
                @csrf
                
                <!-- Step 1: Doctor Selection -->
                <div class="booking-step" id="step-1">
                    <div class="text-center mb-8">
                        <h3 class="text-3xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            Choose Your Doctor
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 text-lg">
                            Select from our team of experienced healthcare professionals
                        </p>
                    </div>
                    
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($doctors as $doctor)
                        <div class="doctor-card group cursor-pointer border-2 border-gray-200 dark:border-gray-600 rounded-2xl p-6 hover:border-blue-500 hover:shadow-xl transition-all duration-300 transform hover:scale-105 bg-gradient-to-br from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20" data-doctor-id="{{ $doctor->id }}">
                            <div class="text-center">
                                <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-200 dark:from-blue-900/50 dark:to-indigo-900/50 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-user-md text-blue-600 dark:text-blue-400 text-2xl"></i>
                                </div>
                                <h4 class="font-bold text-lg text-gray-900 dark:text-gray-100 mb-2">{{ $doctor->name }}</h4>
                                <p class="text-blue-600 dark:text-blue-400 font-medium mb-3">{{ $doctor->department }}</p>
                                <div class="flex items-center justify-center gap-2 mb-4">
                                    <span class="inline-block px-3 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs rounded-full font-medium">
                                        <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                                        Available
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    <div class="flex items-center justify-center gap-1 mb-1">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <span>4.9 (127 reviews)</span>
                                    </div>
                                    <div class="flex items-center justify-center gap-1">
                                        <i class="fas fa-clock text-gray-400"></i>
                                        <span>Next available: Today</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <input type="hidden" name="doctor" id="selectedDoctor" required>
                </div>

                <!-- Step 2: Date & Time Selection -->
                <div class="booking-step hidden" id="step-2">
                    <div class="text-center mb-8">
                        <h3 class="text-3xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            Choose Date & Time
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 text-lg">
                            Select your preferred appointment date and time
                        </p>
                    </div>

                    <!-- Calendar -->
                    <div class="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-600 p-6 mb-6">
                        <div class="flex items-center justify-between mb-6">
                            <h4 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Select Date</h4>
                            <div class="flex space-x-2">
                                <button type="button" id="prevMonth" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-left text-gray-600 dark:text-gray-400"></i>
                                </button>
                                <span id="currentMonth" class="px-4 py-2 text-gray-900 dark:text-gray-100 font-medium"></span>
                                <button type="button" id="nextMonth" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                                    <i class="fas fa-chevron-right text-gray-600 dark:text-gray-400"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Calendar Grid -->
                        <div class="grid grid-cols-7 gap-2 mb-4">
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Sun</div>
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Mon</div>
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Tue</div>
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Wed</div>
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Thu</div>
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Fri</div>
                            <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-3">Sat</div>
                        </div>

                        <!-- Calendar Days -->
                        <div class="grid grid-cols-7 gap-2" id="calendar-days">
                            <!-- Days will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Time Slots -->
                    <div class="mt-6" id="time-slots" style="display: none;">
                        <h4 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Available Times</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <button type="button" class="time-slot p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-center font-medium" data-time="09:00 AM">
                                <div class="text-lg font-bold text-gray-900 dark:text-gray-100">09:00 AM</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                            </button>
                            <button type="button" class="time-slot p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-center font-medium" data-time="10:00 AM">
                                <div class="text-lg font-bold text-gray-900 dark:text-gray-100">10:00 AM</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                            </button>
                            <button type="button" class="time-slot p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-center font-medium" data-time="11:00 AM">
                                <div class="text-lg font-bold text-gray-900 dark:text-gray-100">11:00 AM</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                            </button>
                            <button type="button" class="time-slot p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-center font-medium" data-time="02:00 PM">
                                <div class="text-lg font-bold text-gray-900 dark:text-gray-100">02:00 PM</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                            </button>
                            <button type="button" class="time-slot p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-center font-medium" data-time="03:00 PM">
                                <div class="text-lg font-bold text-gray-900 dark:text-gray-100">03:00 PM</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                            </button>
                            <button type="button" class="time-slot p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 text-center font-medium" data-time="04:00 PM">
                                <div class="text-lg font-bold text-gray-900 dark:text-gray-100">04:00 PM</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Available</div>
                            </button>
                        </div>
                    </div>

                    <input type="hidden" name="date" id="selectedDate" required>
                    <input type="hidden" name="time" id="selectedTime" required>
                </div>

                <!-- Step 3: Patient Information -->
                <div class="booking-step hidden" id="step-3">
                    <div class="text-center mb-8">
                        <h3 class="text-3xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            Your Information
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 text-lg">
                            Please provide your details for the appointment
                        </p>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Patient Name *</label>
                            <input type="text" name="patient_name" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200" placeholder="Enter your full name" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number *</label>
                            <input type="tel" name="phone" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200" placeholder="Enter your phone number" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
                            <input type="email" name="email" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200" placeholder="Enter your email" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password *</label>
                            <input type="password" name="password" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200" placeholder="Create a password" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Profile Image</label>
                            <input type="file" name="image" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200" accept="image/*">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message</label>
                            <textarea name="message" rows="4" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200" placeholder="Tell us how you feel or any specific concerns..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Confirmation -->
                <div class="booking-step hidden" id="step-4">
                    <div class="text-center mb-8">
                        <h3 class="text-3xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                            Confirm Your Appointment
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 text-lg">
                            Please review your appointment details before confirming
                        </p>
                    </div>

                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-200 dark:border-blue-700">
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- Appointment Details -->
                            <div>
                                <h4 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Appointment Details</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-user-md text-blue-600 dark:text-blue-400"></i>
                                        <span class="text-gray-700 dark:text-gray-300">Doctor: <span id="confirm-doctor" class="font-medium"></span></span>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-calendar text-blue-600 dark:text-blue-400"></i>
                                        <span class="text-gray-700 dark:text-gray-300">Date: <span id="confirm-date" class="font-medium"></span></span>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-clock text-blue-600 dark:text-blue-400"></i>
                                        <span class="text-gray-700 dark:text-gray-300">Time: <span id="confirm-time" class="font-medium"></span></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Patient Details -->
                            <div>
                                <h4 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Patient Information</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                                        <span class="text-gray-700 dark:text-gray-300">Name: <span id="confirm-name" class="font-medium"></span></span>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-phone text-blue-600 dark:text-blue-400"></i>
                                        <span class="text-gray-700 dark:text-gray-300">Phone: <span id="confirm-phone" class="font-medium"></span></span>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <i class="fas fa-envelope text-blue-600 dark:text-blue-400"></i>
                                        <span class="text-gray-700 dark:text-gray-300">Email: <span id="confirm-email" class="font-medium"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 p-4 bg-blue-100 dark:bg-blue-900/30 rounded-xl border border-blue-200 dark:border-blue-700">
                            <div class="flex items-start gap-3">
                                <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-1"></i>
                                <div class="text-sm text-blue-800 dark:text-blue-200">
                                    <p class="font-medium mb-1">Important Information:</p>
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>You will receive a confirmation email and SMS</li>
                                        <li>Please arrive 10 minutes before your appointment</li>
                                        <li>Bring a valid ID and insurance card if applicable</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button type="button" id="prevBtn" class="px-6 py-3 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-xl font-medium transition-all duration-200" style="display: none;">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Previous
                    </button>

                    <div class="flex-1"></div>

                    <button type="button" id="nextBtn" class="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        Next Step
                        <i class="fas fa-arrow-right ml-2"></i>
                    </button>

                    <button type="submit" id="submitBtn" class="px-8 py-3 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" style="display: none;">
                        <span id="spinner" class="hidden">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Booking...
                        </span>
                        <span id="buttonText">
                            <i class="fas fa-calendar-check mr-2"></i>
                            Confirm Appointment
                        </span>
                    </button>
                </div>

                <!-- Alert container -->
                <div id="alertContainer" class="mt-6"></div>
            </form>
        </div>
    </div>
</section>

@include('layouts.footer')

<script>
// Multi-step form functionality
let currentStep = 1;
const totalSteps = 4;

document.addEventListener('DOMContentLoaded', function() {
    initializeCalendar();
    initializeBookingInterface();
    updateStepDisplay();
});

function initializeCalendar() {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    generateCalendarDays(currentYear, currentMonth);

    // Month navigation
    document.getElementById('prevMonth').addEventListener('click', () => {
        const newDate = new Date(currentYear, currentMonth - 1, 1);
        generateCalendarDays(newDate.getFullYear(), newDate.getMonth());
    });

    document.getElementById('nextMonth').addEventListener('click', () => {
        const newDate = new Date(currentYear, currentMonth + 1, 1);
        generateCalendarDays(newDate.getFullYear(), newDate.getMonth());
    });
}

function generateCalendarDays(year, month) {
    const calendarDays = document.getElementById('calendar-days');
    const monthNames = ["January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"];

    document.getElementById('currentMonth').textContent = `${monthNames[month]} ${year}`;

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    calendarDays.innerHTML = '';

    for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day text-center p-3 cursor-pointer rounded-xl transition-all duration-200 font-medium';
        dayElement.textContent = date.getDate();

        if (date.getMonth() !== month) {
            dayElement.className += ' text-gray-400 dark:text-gray-600 cursor-not-allowed';
        } else if (date < new Date().setHours(0,0,0,0)) {
            dayElement.className += ' text-gray-400 dark:text-gray-600 cursor-not-allowed';
        } else {
            dayElement.className += ' text-gray-900 dark:text-gray-100 hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:scale-105';
            dayElement.addEventListener('click', () => selectDate(date));
        }

        if (date.toDateString() === new Date().toDateString()) {
            dayElement.className += ' bg-blue-600 text-white hover:bg-blue-700';
        }

        calendarDays.appendChild(dayElement);
    }
}

function selectDate(date) {
    document.getElementById('selectedDate').value = date.toISOString().split('T')[0];
    document.getElementById('time-slots').style.display = 'block';

    // Update selected date display
    document.querySelectorAll('.calendar-day').forEach(day => {
        day.classList.remove('bg-blue-600', 'text-white', 'ring-2', 'ring-blue-500');
    });
    event.target.classList.add('bg-blue-600', 'text-white', 'ring-2', 'ring-blue-500');
}

function initializeBookingInterface() {
    // Doctor selection
    document.querySelectorAll('.doctor-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.doctor-card').forEach(c => {
                c.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'ring-2', 'ring-blue-500');
            });
            this.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'ring-2', 'ring-blue-500');
            document.getElementById('selectedDoctor').value = this.dataset.doctorId;
        });
    });

    // Time slot selection
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.addEventListener('click', function() {
            document.querySelectorAll('.time-slot').forEach(s => {
                s.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'ring-2', 'ring-blue-500');
            });
            this.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'ring-2', 'ring-blue-500');
            document.getElementById('selectedTime').value = this.dataset.time;
        });
    });

    // Navigation buttons
    document.getElementById('nextBtn').addEventListener('click', nextStep);
    document.getElementById('prevBtn').addEventListener('click', prevStep);
}

function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStepDisplay();
            if (currentStep === 4) {
                updateConfirmationDetails();
            }
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

function validateCurrentStep() {
    switch(currentStep) {
        case 1:
            if (!document.getElementById('selectedDoctor').value) {
                showAlert('Please select a doctor', 'error');
                return false;
            }
            break;
        case 2:
            if (!document.getElementById('selectedDate').value || !document.getElementById('selectedTime').value) {
                showAlert('Please select both date and time', 'error');
                return false;
            }
            break;
        case 3:
            const requiredFields = ['patient_name', 'phone', 'email', 'password'];
            for (let field of requiredFields) {
                if (!document.querySelector(`[name="${field}"]`).value) {
                    showAlert('Please fill in all required fields', 'error');
                    return false;
                }
            }
            break;
    }
    return true;
}

function updateStepDisplay() {
    // Hide all steps
    document.querySelectorAll('.booking-step').forEach(step => {
        step.classList.add('hidden');
    });

    // Show current step
    document.getElementById(`step-${currentStep}`).classList.remove('hidden');

    // Update progress indicators
    document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
        const stepNum = index + 1;
        if (stepNum <= currentStep) {
            indicator.classList.remove('bg-gray-300', 'text-gray-600');
            indicator.classList.add('bg-blue-600', 'text-white');
            indicator.nextElementSibling.classList.remove('text-gray-500');
            indicator.nextElementSibling.classList.add('text-blue-600', 'dark:text-blue-400');
        } else {
            indicator.classList.remove('bg-blue-600', 'text-white');
            indicator.classList.add('bg-gray-300', 'text-gray-600');
            indicator.nextElementSibling.classList.remove('text-blue-600', 'dark:text-blue-400');
            indicator.nextElementSibling.classList.add('text-gray-500');
        }
    });

    // Update step lines
    document.querySelectorAll('.step-line').forEach((line, index) => {
        if (index < currentStep - 1) {
            line.classList.remove('bg-gray-200', 'dark:bg-gray-700');
            line.classList.add('bg-blue-600');
        } else {
            line.classList.remove('bg-blue-600');
            line.classList.add('bg-gray-200', 'dark:bg-gray-700');
        }
    });

    // Update current step number
    document.getElementById('current-step').textContent = currentStep;

    // Update navigation buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');

    if (currentStep === 1) {
        prevBtn.style.display = 'none';
    } else {
        prevBtn.style.display = 'block';
    }

    if (currentStep === totalSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'block';
    } else {
        nextBtn.style.display = 'block';
        submitBtn.style.display = 'none';
    }
}

function updateConfirmationDetails() {
    // Get selected doctor name
    const selectedDoctorCard = document.querySelector('.doctor-card.border-blue-500');
    const doctorName = selectedDoctorCard ? selectedDoctorCard.querySelector('h4').textContent : '';

    // Update confirmation display
    document.getElementById('confirm-doctor').textContent = doctorName;
    document.getElementById('confirm-date').textContent = document.getElementById('selectedDate').value;
    document.getElementById('confirm-time').textContent = document.getElementById('selectedTime').value;
    document.getElementById('confirm-name').textContent = document.querySelector('[name="patient_name"]').value;
    document.getElementById('confirm-phone').textContent = document.querySelector('[name="phone"]').value;
    document.getElementById('confirm-email').textContent = document.querySelector('[name="email"]').value;
}

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertClass = type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
                     type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
                     'bg-blue-50 border-blue-200 text-blue-800';

    const iconClass = type === 'error' ? 'fa-exclamation-circle text-red-500' :
                     type === 'success' ? 'fa-check-circle text-green-500' :
                     'fa-info-circle text-blue-500';

    alertContainer.innerHTML = `
        <div class="${alertClass} dark:bg-${type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'}-900/20 border rounded-xl p-4">
            <div class="flex items-center">
                <i class="fas ${iconClass} text-xl mr-3"></i>
                <span>${message}</span>
            </div>
        </div>
    `;

    setTimeout(() => {
        alertContainer.innerHTML = '';
    }, 5000);
}

// Form submission
document.getElementById('appointmentForm').addEventListener('submit', function (event) {
    event.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const spinner = document.getElementById('spinner');
    const buttonText = document.getElementById('buttonText');

    // Show loading state
    spinner.classList.remove('hidden');
    buttonText.classList.add('hidden');

    // Submit form data via fetch
    fetch("{{ route('appointment.store') }}", {
        method: "POST",
        body: formData,
        headers: {
            "X-CSRF-TOKEN": document.querySelector('input[name="_token"]').value
        }
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => {
                throw err;
            });
        }
        return response.json();
    })
    .then(data => {
        // Hide loading state
        spinner.classList.add('hidden');
        buttonText.classList.remove('hidden');

        if (data.success) {
            showAlert('Appointment booked successfully! You will receive a confirmation email and SMS.', 'success');

            // Reset form after delay
            setTimeout(() => {
                form.reset();
                currentStep = 1;
                updateStepDisplay();
                // Reset selections
                document.querySelectorAll('.doctor-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'ring-2', 'ring-blue-500');
                });
                document.querySelectorAll('.time-slot').forEach(s => {
                    s.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20', 'ring-2', 'ring-blue-500');
                });
                document.getElementById('time-slots').style.display = 'none';

                // Redirect to login or dashboard
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            }, 3000);
        } else {
            showAlert(data.message || 'Booking failed. Please try again.', 'error');
        }
    })
    .catch(error => {
        // Hide loading state
        spinner.classList.add('hidden');
        buttonText.classList.remove('hidden');

        if (error.errors) {
            let errorMessages = [];
            for (const [field, messages] of Object.entries(error.errors)) {
                errorMessages.push(...messages);
            }
            showAlert('Please fix the following errors: ' + errorMessages.join(', '), 'error');
        } else {
            showAlert('Something went wrong. Please try again later.', 'error');
        }
    });
});
</script>
</body>
</html>
