@include('layouts.header')

<body class="bg-medical-bg-light">
@include('layouts.navbar')

<!-- Modern Breadcrumb -->
<section class="bg-gradient-to-r from-medical-primary to-medical-secondary py-12">
    <div class="container mx-auto px-4">
        <div class="flex items-center space-x-2 text-white/80 text-sm mb-4">
            <a href="/" class="hover:text-white transition-colors">Home</a>
            <i data-lucide="chevron-right" class="h-4 w-4"></i>
            <span class="text-white">Book Appointment</span>
        </div>
        <h1 class="text-3xl md:text-4xl font-bold text-white">Book Your Appointment</h1>
        <p class="text-white/90 mt-2 max-w-2xl">Schedule your consultation with our qualified medical professionals</p>
    </div>
</section>

<!-- Enhanced Booking Calendar Section -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Progress Steps -->
            <div class="mb-12">
                <div class="flex items-center justify-center space-x-4 md:space-x-8">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-medical-primary text-white rounded-full flex items-center justify-center font-semibold">1</div>
                        <span class="ml-2 text-medical-primary font-medium">Select Date & Time</span>
                    </div>
                    <div class="w-8 h-0.5 bg-medical-border-light"></div>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-medical-border-light text-medical-neutral-400 rounded-full flex items-center justify-center font-semibold">2</div>
                        <span class="ml-2 text-medical-neutral-400 font-medium">Patient Details</span>
                    </div>
                    <div class="w-8 h-0.5 bg-medical-border-light"></div>
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-medical-border-light text-medical-neutral-400 rounded-full flex items-center justify-center font-semibold">3</div>
                        <span class="ml-2 text-medical-neutral-400 font-medium">Confirmation</span>
                    </div>
                </div>
            </div>

            <!-- Main Booking Interface -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="grid lg:grid-cols-3 gap-0">
                    <!-- Calendar Section -->
                    <div class="lg:col-span-2 p-8">
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-medical-neutral-600 mb-2">Select Date & Time</h2>
                            <p class="text-medical-neutral-500">Choose your preferred appointment slot</p>
                        </div>

                        <!-- Visual Calendar -->
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- Date Selection -->
                            <div>
                                <h3 class="text-lg font-semibold text-medical-neutral-600 mb-4">Choose Date</h3>
                                <div class="bg-medical-bg-light p-6 rounded-xl">
                                    <div class="calendar-container">
                                        <div class="flex items-center justify-between mb-4">
                                            <button type="button" id="prevMonth" class="p-2 hover:bg-white rounded-lg transition-colors">
                                                <i data-lucide="chevron-left" class="h-5 w-5 text-medical-neutral-600"></i>
                                            </button>
                                            <h4 id="currentMonth" class="font-semibold text-medical-neutral-600"></h4>
                                            <button type="button" id="nextMonth" class="p-2 hover:bg-white rounded-lg transition-colors">
                                                <i data-lucide="chevron-right" class="h-5 w-5 text-medical-neutral-600"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- Calendar Grid -->
                                        <div class="grid grid-cols-7 gap-1 mb-2">
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Sun</div>
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Mon</div>
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Tue</div>
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Wed</div>
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Thu</div>
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Fri</div>
                                            <div class="text-center text-xs font-medium text-medical-neutral-500 py-2">Sat</div>
                                        </div>
                                        <div id="calendarDays" class="grid grid-cols-7 gap-1"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time Selection -->
                            <div>
                                <h3 class="text-lg font-semibold text-medical-neutral-600 mb-4">Available Times</h3>
                                <div id="timeSlotContainer" class="space-y-4">
                                    <div class="text-center py-8 text-medical-neutral-400">
                                        <i data-lucide="calendar" class="h-12 w-12 mx-auto mb-2"></i>
                                        <p>Please select a date to see available times</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected Appointment Summary -->
                        <div id="appointmentSummary" class="hidden mt-8 p-6 bg-medical-bg-light rounded-xl">
                            <h4 class="font-semibold text-medical-neutral-600 mb-2">Selected Appointment</h4>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <i data-lucide="calendar" class="h-5 w-5 text-medical-primary mr-2"></i>
                                    <span id="selectedDateText" class="text-medical-neutral-600"></span>
                                </div>
                                <div class="flex items-center">
                                    <i data-lucide="clock" class="h-5 w-5 text-medical-primary mr-2"></i>
                                    <span id="selectedTimeText" class="text-medical-neutral-600"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Form Sidebar -->
                    <div class="bg-gradient-to-br from-medical-bg-light to-white p-8 border-l border-medical-border-light">
                        <div class="sticky top-8">
                            <h3 class="text-xl font-bold text-medical-neutral-600 mb-6">Patient Information</h3>
                            
                            <form id="appointmentForm" class="space-y-4">
                                @csrf
                                
                                <!-- Hidden inputs for date/time -->
                                <input type="hidden" name="date" id="selectedDate">
                                <input type="hidden" name="time" id="selectedTime">
                                
                                <!-- Patient Name -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Patient Name *</label>
                                    <input type="text" name="patient_name" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Enter full name">
                                </div>

                                <!-- Phone -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Enter phone number">
                                </div>

                                <!-- Email -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Email Address *</label>
                                    <input type="email" name="email" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Enter email address">
                                </div>

                                <!-- Password -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Password *</label>
                                    <input type="password" name="password" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Create password">
                                </div>

                                <!-- Doctor Selection -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Select Doctor *</label>
                                    <select name="doctor" required
                                            class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200">
                                        <option value="">Choose your doctor</option>
                                        @foreach($doctors as $doctor)
                                            <option value="{{ $doctor->id }}">{{ $doctor->name }} - {{ $doctor->department }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Profile Image -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Profile Image</label>
                                    <div class="border-2 border-dashed border-medical-border-light rounded-lg p-4 text-center hover:border-medical-primary transition-colors duration-200">
                                        <input type="file" name="image" id="imageUpload" class="hidden" accept="image/*">
                                        <label for="imageUpload" class="cursor-pointer">
                                            <i data-lucide="upload" class="h-6 w-6 text-medical-neutral-400 mx-auto mb-1"></i>
                                            <p class="text-sm text-medical-neutral-500">Upload image</p>
                                        </label>
                                    </div>
                                </div>

                                <!-- Message -->
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Message</label>
                                    <textarea name="message" rows="3"
                                              class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                              placeholder="Describe your symptoms..."></textarea>
                                </div>

                                <!-- Submit Button -->
                                <button type="submit" disabled id="submitBtn"
                                        class="w-full bg-medical-neutral-300 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 disabled:cursor-not-allowed">
                                    <span id="spinner" class="hidden">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Booking...
                                    </span>
                                    <span id="buttonText" class="flex items-center justify-center">
                                        <i data-lucide="calendar-plus" class="mr-2 h-5 w-5"></i>
                                        Select Date & Time First
                                    </span>
                                </button>

                                <!-- Alert container -->
                                <div id="alertContainer" class="mt-4"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@include('layouts.footer')

<script>
// Initialize Lucide icons and calendar
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    initializeCalendar();
});

// Calendar functionality will be added in the next part
function initializeCalendar() {
    // Calendar implementation
    console.log('Calendar initialized');
}
</script>

</body>
</html>
