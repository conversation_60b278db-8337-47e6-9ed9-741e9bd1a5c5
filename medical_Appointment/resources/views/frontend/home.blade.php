﻿@include('layouts.header')

<!-- Loading Spinner -->
<div id="loading" class="fixed inset-0 bg-white/90 backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-medical-primary"></div>
</div>

@include('layouts.navbar')

<!-- Hero Section -->
<main class="flex-grow">
    <section class="py-16 md:py-24">
        <div class="container mx-auto px-4">
            <div class="max-w-7xl mx-auto">
                <!-- Main Profile Card -->
                <div class="mb-8 overflow-hidden shadow-2xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl">
                    <div class="grid lg:grid-cols-5 gap-0">
                        <!-- Profile Image -->
                        <div class="lg:col-span-2 relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 flex items-center justify-center overflow-hidden">
                            <div class="w-full h-96 lg:h-[500px] relative">
                                <img
                                    src="{{ asset('fintan/Drekochin portrait.png') }}"
                                    alt="Dr. Fintan Ekochin"
                                    class="w-full h-full object-cover object-center"
                                    style="display: block;"
                                    onError="this.style.display='none'; this.parentElement.querySelector('.fallback-avatar').style.display='flex';"
                                />
                                <div
                                    class="fallback-avatar absolute inset-0 bg-gradient-to-br from-blue-100 to-indigo-200 dark:from-blue-800 dark:to-indigo-900 flex items-center justify-center text-6xl font-bold text-blue-600 dark:text-blue-300"
                                    style="display: none;"
                                >
                                    FE
                                </div>
                            </div>
                        </div>

                        <!-- Profile Info - Updated with detailed content from fintan -->
                        <div class="lg:col-span-3 p-8 lg:p-12 flex flex-col justify-center bg-gradient-to-r from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20">
                            <div class="mb-4">
                                <h1 class="text-4xl md:text-5xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                    Dr. Fintan Ekochin, MD
                                </h1>
                                <p class="text-xl md:text-2xl text-blue-600 dark:text-blue-400 font-medium mb-6">
                                    Fellow WACP • Neurologist • Integrative Medicine Specialist
                                </p>
                            </div>

                            <div class="space-y-4 mb-8">
                                <p class="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                                    Dr. Ekochin Fintan is one of two generations of the EKOCHIN Family of Doctors. He largely grew up in
                                    Nigeria with some years of childhood spent in Austria, where he added German to his Igbo and English
                                    language proficiency.
                                </p>

                                <p class="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                                    After completing Primary and Secondary schools in Enugu and Nsukka, he earned an MBBS from the premier
                                    University of Nigeria, College of Medicine. Post graduation activities were first in the Paklose
                                    Specialist Hospital before going to do House training in Internal Medicine at the University Teaching
                                    Hospital both in New Delhi (2011).
                                </p>

                                <p class="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                                    He later completed neurology residency in India and the USA, earning Fellowship of the West African
                                    College of Physicians. He currently serves as Head of Neurology at ESUT Teaching Hospital Enugu and
                                    Senior Lecturer for Neurophysiology at Godfrey Okoye University.
                                </p>

                                <p class="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                                    Dr. Ekochin served as Commissioner for Health, Enugu State (2017-2019), bringing extensive leadership
                                    experience to healthcare administration and policy development.
                                </p>

                                <div class="flex flex-wrap gap-2 mb-6">
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 text-sm rounded-full font-medium">Fellow WACP</span>
                                    <span class="px-3 py-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 text-sm rounded-full font-medium">Integrative Medicine</span>
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-sm rounded-full font-medium">Lifestyle Medicine</span>
                                    <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 text-sm rounded-full font-medium">Former Health Commissioner</span>
                                </div>
                            </div>

                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="/appointment" class="flex-1">
                                    <button class="w-full py-4 px-8 text-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-xl font-semibold">
                                        Book Consultation
                                        <i class="fas fa-arrow-right ml-2"></i>
                                    </button>
                                </a>
                                <a href="/about" class="flex-1">
                                    <button class="w-full py-4 px-8 text-lg border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50 dark:border-blue-700 dark:hover:border-blue-600 dark:hover:bg-blue-900/20 transition-all duration-300 rounded-xl font-semibold text-blue-600 dark:text-blue-400">
                                        Learn More
                                    </button>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>





















    @include('layouts.footer')

    <script>
    // Hide loading spinner when page loads
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('loading').style.display = 'none';
    });







    </script>
</body>

</html>