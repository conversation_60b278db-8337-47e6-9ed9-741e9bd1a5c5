@include('layouts.header')

<!-- Loading Spinner -->
<div id="loading" class="fixed inset-0 bg-white/90 backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-medical-primary"></div>
</div>

@include('layouts.navbar')

<!-- Hero Section -->
<main class="flex-grow">
    <section class="py-16 md:py-24">
        <div class="container mx-auto px-4">
            <div class="max-w-7xl mx-auto">
                <!-- Main Profile Card -->
                <div class="mb-8 overflow-hidden shadow-2xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl">
                    <div class="grid lg:grid-cols-5 gap-0">
                        <!-- Profile Image -->
                        <div class="lg:col-span-2 relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 flex items-center justify-center overflow-hidden">
                            <div class="w-full h-96 lg:h-[500px] relative">
                                <img
                                    src="{{ asset('assets/images/banner_img.png') }}"
                                    alt="Medical Professional"
                                    class="w-full h-full object-cover object-center"
                                    style="display: block;"
                                />
                                <div class="absolute inset-0 bg-gradient-to-t from-blue-600/20 to-transparent"></div>
                            </div>
                        </div>

                        <!-- Profile Info -->
                        <div class="lg:col-span-3 p-8 lg:p-12 flex flex-col justify-center bg-gradient-to-r from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20">
                            <div class="mb-4">
                                <h1 class="text-4xl md:text-5xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                    Professional Medical Care
                                </h1>
                                <p class="text-xl md:text-2xl text-blue-600 dark:text-blue-400 font-medium mb-6">
                                    Expert Healthcare • Advanced Treatment • Compassionate Care
                                </p>
                            </div>

                            <div class="space-y-4 mb-8">
                                <p class="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                                    Our medical appointment system connects you with qualified healthcare professionals
                                    who are dedicated to providing exceptional medical care. We offer comprehensive
                                    healthcare services with a focus on patient-centered treatment.
                                </p>

                                <p class="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                                    Experience modern healthcare with our easy-to-use appointment booking system,
                                    qualified medical professionals, and comprehensive treatment options designed
                                    to meet your healthcare needs.
                                </p>

                                <div class="flex flex-wrap gap-2 mb-6">
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 text-sm rounded-full font-medium">Expert Doctors</span>
                                    <span class="px-3 py-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 text-sm rounded-full font-medium">Modern Facilities</span>
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-sm rounded-full font-medium">24/7 Support</span>
                                    <span class="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 text-sm rounded-full font-medium">Advanced Technology</span>
                                </div>
                            </div>

                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="/appointment" class="flex-1">
                                    <button class="w-full py-4 px-8 text-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-xl font-semibold">
                                        <i class="fas fa-calendar-plus mr-2"></i>
                                        Book Appointment
                                    </button>
                                </a>
                                <a href="/login" class="flex-1">
                                    <button class="w-full py-4 px-8 text-lg border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50 dark:border-blue-700 dark:hover:border-blue-600 dark:hover:bg-blue-900/20 transition-all duration-300 rounded-xl font-semibold text-blue-600 dark:text-blue-400">
                                        <i class="fas fa-sign-in-alt mr-2"></i>
                                        Patient Login
                                    </button>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Features Grid -->
                <div class="grid md:grid-cols-3 gap-6 mb-12">
                    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-2xl">
                        <div class="p-8 text-center">
                            <i class="fas fa-video text-blue-600 dark:text-blue-400 text-5xl mb-4"></i>
                            <h3 class="font-bold text-xl mb-3 text-gray-900 dark:text-gray-100">Online Consultations</h3>
                            <p class="text-base text-gray-600 dark:text-gray-300">Connect with doctors through secure video calls from anywhere</p>
                        </div>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-2xl">
                        <div class="p-8 text-center">
                            <i class="fas fa-calendar-check text-blue-600 dark:text-blue-400 text-5xl mb-4"></i>
                            <h3 class="font-bold text-xl mb-3 text-gray-900 dark:text-gray-100">Smart Scheduling</h3>
                            <p class="text-base text-gray-600 dark:text-gray-300">AI-powered appointment booking with real-time availability</p>
                        </div>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-2xl">
                        <div class="p-8 text-center">
                            <i class="fas fa-shield-alt text-blue-600 dark:text-blue-400 text-5xl mb-4"></i>
                            <h3 class="font-bold text-xl mb-3 text-gray-900 dark:text-gray-100">Secure & Private</h3>
                            <p class="text-base text-gray-600 dark:text-gray-300">HIPAA compliant with end-to-end encryption for your safety</p>
                        </div>
                    </div>
                </div>

                <!-- Specialties Grid -->
                <div class="grid lg:grid-cols-3 gap-4">
                    <div class="text-center p-6 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 hover:bg-white dark:hover:bg-gray-800 rounded-2xl">
                        <i class="fas fa-heart text-blue-600 dark:text-blue-400 text-4xl mb-3"></i>
                        <h3 class="font-bold text-base mb-2 text-gray-900 dark:text-gray-100">Cardiology</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Heart and cardiovascular care</p>
                    </div>

                    <div class="text-center p-6 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 hover:bg-white dark:hover:bg-gray-800 rounded-2xl">
                        <i class="fas fa-brain text-blue-600 dark:text-blue-400 text-4xl mb-3"></i>
                        <h3 class="font-bold text-base mb-2 text-gray-900 dark:text-gray-100">Neurology</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Brain and nervous system treatment</p>
                    </div>

                    <div class="text-center p-6 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 hover:bg-white dark:hover:bg-gray-800 rounded-2xl">
                        <i class="fas fa-user-md text-blue-600 dark:text-blue-400 text-4xl mb-3"></i>
                        <h3 class="font-bold text-base mb-2 text-gray-900 dark:text-gray-100">General Medicine</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Comprehensive primary healthcare</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Appointment Booking Section -->
    <section class="py-20 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
        <div class="container mx-auto px-4">
            <div class="max-w-5xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-8 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Book Your Appointment Today
                </h2>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-l-4 border-blue-600 shadow-xl rounded-2xl">
                    <div class="p-8">
                        <blockquote class="text-lg md:text-xl italic text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                            "Experience modern healthcare with our comprehensive medical services.
                            Our qualified doctors and advanced facilities ensure you receive the best
                            possible care tailored to your individual needs."
                        </blockquote>
                        <p class="font-semibold text-lg text-blue-600 dark:text-blue-400">— Medical Team</p>
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mt-12">
                    <div class="flex items-start gap-4 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-check-circle text-green-500 text-2xl mt-1"></i>
                        <span class="text-base text-gray-700 dark:text-gray-300 leading-relaxed">Expert medical professionals with years of experience</span>
                    </div>

                    <div class="flex items-start gap-4 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-check-circle text-green-500 text-2xl mt-1"></i>
                        <span class="text-base text-gray-700 dark:text-gray-300 leading-relaxed">State-of-the-art medical equipment and facilities</span>
                    </div>

                    <div class="flex items-start gap-4 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-check-circle text-green-500 text-2xl mt-1"></i>
                        <span class="text-base text-gray-700 dark:text-gray-300 leading-relaxed">Comprehensive healthcare services across multiple specialties</span>
                    </div>

                    <div class="flex items-start gap-4 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-check-circle text-green-500 text-2xl mt-1"></i>
                        <span class="text-base text-gray-700 dark:text-gray-300 leading-relaxed">Patient-centered care with personalized treatment plans</span>
                    </div>
                </div>

                <div class="mt-12">
                    <a href="/appointment">
                        <button class="py-4 px-8 text-lg bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 rounded-xl font-semibold">
                            <i class="fas fa-calendar-plus mr-2"></i>
                            Begin Your Health Journey
                        </button>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!--============================
        ABOUT START
    ==============================-->
    <section class="about pt_100 xs_pt_70 pb_100 xs_pb_70">
        <div class="container">
            <div class="row">
                <div class="col-xl-6 col-sm-9  col-lg-5 col-md-7 wow fadeInLeft" data-wow-duration="1s">
                    <div class="about_img">
                        <div class="about_img_1">
                            <img src="assets/images/about.jpg" alt="about img" class="img-fluid w-100">
                        </div>
                    </div>
                </div>
                <div class="col-xl-6 col-sm-12 col-lg-7  wow fadeInRight" data-wow-duration="1s">
                <div class="common_heading">
                    <h5>About Us</h5>
                    <h2>The Great Place of Medical Hospital Center</h2>
                    <p>At our Medical Hospital Center, we are dedicated to providing exceptional healthcare services with compassion and expertise. Our state-of-the-art facilities and highly skilled medical professionals ensure that you receive the best possible care. Whether it's routine check-ups, advanced treatments, or emergency services, we are here to support your health and well-being every step of the way.</p>
                </div>

                    <ul class="about_iteam d-flex flex-wrap">
                        <li>Ambulance Services</li>
                        <li>Oxygens on Wheel</li>
                        <li>Pharmacy on Clinic</li>
                        <li>On duty Doctors</li>
                        <li>24/7 Medical Emergency</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    <!--============================
        ABOUT END
    ==============================-->


    <!--============================
        SERVICE START
    ==============================-->
    <section class="service" style="background: url(assets/images/service_bg.jpg);">
    <div class="service_overlay pt_100 xs_pt_70 pb_100 xs_pb_70">
        <div class="container">
            <div class="row">
                <div class="col-xl-12">
                    <div class="common_heading center_heading mb_15">
                        <h5>Our Services</h5>
                        <h2>Comprehensive Medical Care</h2>
                    </div>
                </div>
            </div>
            <div class="row service_slider">
                <!-- Service 1: Online Monitoring -->
                <div class="col-xxl-4 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_service">
                        <div class="service_img">
                            <span class="tf_service_icon"><i class="fas fa-eye"></i></span>
                            <img src="https://www.shutterstock.com/image-photo/african-female-doctor-holding-phone-600nw-**********.jpg" alt="Online Monitoring" class="img-fluid w-100">
                        </div>
                        <div class="service_text">
                            <a  class="service_heading">Online Monitoring</a>
                            <p>Stay connected with our 24/7 online monitoring services. Track your health remotely with expert guidance from our medical team.</p>
                           
                        </div>
                    </div>
                </div>

                <!-- Service 2: Heart Surgery -->
                <div class="col-xxl-4 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_service">
                        <div class="service_img">
                            <span class="tf_service_icon tf_service_icon2"><i class="fas fa-heartbeat"></i></span>
                            <img src="assets/images/Heart_Surgery.jpeg" alt="Heart Surgery" class="img-fluid w-100">
                        </div>
                        <div class="service_text">
                            <a  class="service_heading">Heart Surgery</a>
                            <p>Advanced heart surgery procedures performed by our skilled cardiologists to ensure your heart health is in the best hands.</p>
                           
                        </div>
                    </div>
                </div>

                <!-- Service 3: Diagnosis & Research -->
                <div class="col-xxl-4 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_service">
                        <div class="service_img">
                            <span class="tf_service_icon tf_service_icon3"><i class="fad fa-capsules"></i></span>
                            <img src="https://www.shutterstock.com/shutterstock/videos/**********/thumb/1.jpg?ip=x480" alt="Diagnosis & Research" class="img-fluid w-100">
                        </div>
                        <div class="service_text">
                            <a  class="service_heading">Diagnosis & Research</a>
                            <p>Cutting-edge diagnostic tools and research-driven approaches to provide accurate and timely medical solutions.</p>
                           
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
    <!--============================
        SERVICE END
    ==============================-->





<!--============================
    PROCESS START
==============================-->
<section class="process pt_100 xs_pt_70 pb_95 xs_pb_65" style="background: url(assets/images/work_bg.jpg);">
    <div class="container process_shape">
        <div class="row">
            <div class="col-xl-12">
                <div class="common_heading center_heading mb_25">
                    <h5>How We Work</h5>
                    <h2>Our Working Process</h2>
                </div>
            </div>
        </div>
        <div class="work_process_area">
            <div class="row">
                <!-- Step 1: Fill the Form -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num1">01</span>
                        <h4>Fill the Form</h4>
                        <p>Start by filling out our simple online form with your basic details and medical history. This helps us understand your needs better.</p>
                    </div>
                </div>

                <!-- Step 2: Book an Appointment -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num2">02</span>
                        <h4>Book an Appointment</h4>
                        <p>Choose a convenient date and time for your appointment. Our team will confirm your booking and provide all necessary details.</p>
                    </div>
                </div>

                <!-- Step 3: Check-Ups -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num3">03</span>
                        <h4>Check-Ups</h4>
                        <p>Visit our facility for a thorough check-up. Our experienced doctors will assess your health and recommend the best course of action.</p>
                    </div>
                </div>

                <!-- Step 4: Get Reports -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num4">04</span>
                        <h4>Get Reports</h4>
                        <p>Receive detailed reports and personalized treatment plans. Our team will guide you through the next steps for your health journey.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--============================
    PROCESS END
==============================-->


    <!--============================
        MODERN APPOINTMENT BOOKING START
    ==============================-->
    <section class="py-16 md:py-24 bg-gradient-to-br from-medical-primary/5 to-medical-accent/5 dark:from-gray-900 dark:to-gray-800">
        <div class="container mx-auto max-w-6xl px-4">
            <!-- Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Schedule Your Consultation
                </h2>
                <p class="text-gray-600 dark:text-gray-400 text-lg">
                    Choose the perfect time that works for you with our intelligent scheduling system
                </p>
            </div>

            <div class="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-2xl shadow-2xl border-0 dark:border-gray-700">
                <div class="p-6 md:p-8">
                    <!-- Booking Progress -->
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                                <span class="text-sm font-medium text-blue-600">Doctor & Date</span>
                            </div>
                            <div class="flex-1 h-1 bg-gray-200 dark:bg-gray-700 mx-4 rounded"></div>
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                                <span class="text-sm font-medium text-gray-500">Your Info</span>
                            </div>
                            <div class="flex-1 h-1 bg-gray-200 dark:bg-gray-700 mx-4 rounded"></div>
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                                <span class="text-sm font-medium text-gray-500">Confirm</span>
                            </div>
                        </div>
                    </div>

                    <form id="appointmentForm" class="space-y-8">
                        @csrf

                        <!-- Doctor Selection -->
                        <div class="space-y-4">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Select Doctor & Department</h3>
                            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($doctors as $doctor)
                                <div class="doctor-card border-2 border-gray-200 dark:border-gray-600 rounded-xl p-4 cursor-pointer hover:border-blue-500 hover:shadow-lg transition-all duration-300" data-doctor-id="{{ $doctor->id }}">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user-md text-blue-600 dark:text-blue-400"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900 dark:text-gray-100">{{ $doctor->name }}</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $doctor->department }}</p>
                                            <span class="inline-block px-2 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs rounded-full mt-1">
                                                {{ $doctor->availability }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            <input type="hidden" name="doctor" id="selectedDoctor" required>
                        </div>

                        <!-- Visual Calendar -->
                        <div class="space-y-4">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Choose Date & Time</h3>

                            <!-- Calendar View Selector -->
                            <div class="flex space-x-2 mb-6">
                                <button type="button" class="view-btn px-4 py-2 bg-blue-600 text-white rounded-lg font-medium transition-colors duration-200" data-view="suggestions">
                                    Smart Suggestions
                                </button>
                                <button type="button" class="view-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors duration-200" data-view="calendar">
                                    Calendar View
                                </button>
                            </div>
                            <!-- Smart Suggestions View -->
                            <div id="suggestions-view" class="calendar-view">
                                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <!-- Today -->
                                    <div class="suggestion-card bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-300" data-date="today" data-time="10:00 AM">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-green-600 dark:text-green-400">Available Today</span>
                                            <span class="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs px-2 py-1 rounded-full">20% OFF</span>
                                        </div>
                                        <p class="font-semibold text-gray-900 dark:text-gray-100">10:00 AM</p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Same day consultation</p>
                                    </div>

                                    <!-- Tomorrow -->
                                    <div class="suggestion-card bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-300" data-date="tomorrow" data-time="2:00 PM">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-blue-600 dark:text-blue-400">Available Tomorrow</span>
                                            <span class="bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-full">15% OFF</span>
                                        </div>
                                        <p class="font-semibold text-gray-900 dark:text-gray-100">2:00 PM</p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Next day appointment</p>
                                    </div>

                                    <!-- This Week -->
                                    <div class="suggestion-card bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-700 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all duration-300" data-date="week" data-time="11:00 AM">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-medium text-purple-600 dark:text-purple-400">This Week</span>
                                            <span class="bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 text-xs px-2 py-1 rounded-full">Popular</span>
                                        </div>
                                        <p class="font-semibold text-gray-900 dark:text-gray-100">11:00 AM</p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Flexible timing</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Calendar View -->
                            <div id="calendar-view" class="calendar-view hidden">
                                <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-600 p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Select Date</h4>
                                        <div class="flex space-x-2">
                                            <button type="button" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                                                <i class="fas fa-chevron-left text-gray-600 dark:text-gray-400"></i>
                                            </button>
                                            <button type="button" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                                                <i class="fas fa-chevron-right text-gray-600 dark:text-gray-400"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Simple Calendar Grid -->
                                    <div class="grid grid-cols-7 gap-2 mb-4">
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Sun</div>
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Mon</div>
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Tue</div>
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Wed</div>
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Thu</div>
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Fri</div>
                                        <div class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">Sat</div>
                                    </div>

                                    <!-- Calendar Days -->
                                    <div class="grid grid-cols-7 gap-2" id="calendar-days">
                                        <!-- Days will be populated by JavaScript -->
                                    </div>
                                </div>

                                <!-- Time Slots -->
                                <div class="mt-6" id="time-slots" style="display: none;">
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Available Times</h4>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                        <button type="button" class="time-slot p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200" data-time="09:00 AM">09:00 AM</button>
                                        <button type="button" class="time-slot p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200" data-time="10:00 AM">10:00 AM</button>
                                        <button type="button" class="time-slot p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200" data-time="11:00 AM">11:00 AM</button>
                                        <button type="button" class="time-slot p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200" data-time="02:00 PM">02:00 PM</button>
                                        <button type="button" class="time-slot p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200" data-time="03:00 PM">03:00 PM</button>
                                        <button type="button" class="time-slot p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200" data-time="04:00 PM">04:00 PM</button>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="date" id="selectedDate" required>
                            <input type="hidden" name="time" id="selectedTime" required>
                        </div>

                        <!-- Patient Information -->
                        <div class="space-y-4">
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Your Information</h3>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Patient Name *</label>
                                    <input type="text" name="patient_name" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="Enter your full name" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="Enter your phone number" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
                                    <input type="email" name="email" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="Enter your email" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password *</label>
                                    <input type="password" name="password" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="Create a password" required>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Profile Image</label>
                                    <input type="file" name="image" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" accept="image/*">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message</label>
                                    <textarea name="message" rows="4" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="Tell us how you feel or any specific concerns..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-center">
                            <button type="submit" class="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                <span id="spinner" class="hidden">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    Booking Appointment...
                                </span>
                                <span id="buttonText">
                                    <i class="fas fa-calendar-check mr-2"></i>
                                    Book Appointment
                                </span>
                            </button>
                        </div>

                        <!-- Alert container -->
                        <div id="alertContainer" class="mt-4"></div>
                    </form>
                </div>
            </div>
        </div>
    </section>
    <!--============================
        APPOINMENT END
    ==============================-->

    @include('layouts.footer')

    <script>
    // Hide loading spinner when page loads
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('loading').style.display = 'none';

        // Initialize calendar
        initializeCalendar();

        // Initialize interactive elements
        initializeBookingInterface();
    });

    function initializeCalendar() {
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        generateCalendarDays(currentYear, currentMonth);
    }

    function generateCalendarDays(year, month) {
        const calendarDays = document.getElementById('calendar-days');
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        calendarDays.innerHTML = '';

        for (let i = 0; i < 42; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);

            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day text-center p-2 cursor-pointer rounded-lg transition-all duration-200';
            dayElement.textContent = date.getDate();

            if (date.getMonth() !== month) {
                dayElement.className += ' text-gray-400 dark:text-gray-600';
            } else if (date < new Date().setHours(0,0,0,0)) {
                dayElement.className += ' text-gray-400 dark:text-gray-600 cursor-not-allowed';
            } else {
                dayElement.className += ' text-gray-900 dark:text-gray-100 hover:bg-blue-100 dark:hover:bg-blue-900/50';
                dayElement.addEventListener('click', () => selectDate(date));
            }

            if (date.toDateString() === new Date().toDateString()) {
                dayElement.className += ' bg-blue-600 text-white';
            }

            calendarDays.appendChild(dayElement);
        }
    }

    function selectDate(date) {
        document.getElementById('selectedDate').value = date.toISOString().split('T')[0];
        document.getElementById('time-slots').style.display = 'block';

        // Update selected date display
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.classList.remove('bg-blue-600', 'text-white');
        });
        event.target.classList.add('bg-blue-600', 'text-white');
    }

    function initializeBookingInterface() {
        // View mode switching
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.dataset.view;

                // Update button states
                document.querySelectorAll('.view-btn').forEach(b => {
                    b.classList.remove('bg-blue-600', 'text-white');
                    b.classList.add('bg-gray-200', 'text-gray-700');
                });
                this.classList.remove('bg-gray-200', 'text-gray-700');
                this.classList.add('bg-blue-600', 'text-white');

                // Show/hide views
                document.querySelectorAll('.calendar-view').forEach(v => v.classList.add('hidden'));
                document.getElementById(view + '-view').classList.remove('hidden');
            });
        });

        // Doctor selection
        document.querySelectorAll('.doctor-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.doctor-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                });
                this.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                document.getElementById('selectedDoctor').value = this.dataset.doctorId;
            });
        });

        // Smart suggestion selection
        document.querySelectorAll('.suggestion-card').forEach(card => {
            card.addEventListener('click', function() {
                const date = this.dataset.date;
                const time = this.dataset.time;

                // Set date based on suggestion
                const today = new Date();
                let selectedDate;

                if (date === 'today') {
                    selectedDate = today;
                } else if (date === 'tomorrow') {
                    selectedDate = new Date(today);
                    selectedDate.setDate(today.getDate() + 1);
                } else if (date === 'week') {
                    selectedDate = new Date(today);
                    selectedDate.setDate(today.getDate() + 3);
                }

                document.getElementById('selectedDate').value = selectedDate.toISOString().split('T')[0];
                document.getElementById('selectedTime').value = time;

                // Visual feedback
                document.querySelectorAll('.suggestion-card').forEach(c => {
                    c.classList.remove('ring-2', 'ring-blue-500');
                });
                this.classList.add('ring-2', 'ring-blue-500');
            });
        });

        // Time slot selection
        document.querySelectorAll('.time-slot').forEach(slot => {
            slot.addEventListener('click', function() {
                document.querySelectorAll('.time-slot').forEach(s => {
                    s.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                });
                this.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                document.getElementById('selectedTime').value = this.dataset.time;
            });
        });
    }

    // Form submission
    document.getElementById('appointmentForm').addEventListener('submit', function (event) {
        event.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const alertContainer = document.getElementById('alertContainer');
        const spinner = document.getElementById('spinner');
        const buttonText = document.getElementById('buttonText');

        // Clear previous alerts
        alertContainer.innerHTML = '';

        // Show loading state
        spinner.classList.remove('hidden');
        buttonText.classList.add('hidden');

        // Submit form data via fetch
        fetch("{{ route('appointment.store') }}", {
            method: "POST",
            body: formData,
            headers: {
                "X-CSRF-TOKEN": document.querySelector('input[name="_token"]').value
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => {
                    throw err;
                });
            }
            return response.json();
        })
        .then(data => {
            // Hide loading state
            spinner.classList.add('hidden');
            buttonText.classList.remove('hidden');

            if (data.success) {
                // Show modern success message
                alertContainer.innerHTML = `
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-xl p-4">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                            <div>
                                <h4 class="text-green-800 dark:text-green-200 font-semibold">Appointment Booked Successfully!</h4>
                                <p class="text-green-700 dark:text-green-300 text-sm mt-1">${data.message}</p>
                            </div>
                        </div>
                    </div>
                `;

                // Reset form
                form.reset();

                // Reset selections
                document.querySelectorAll('.doctor-card').forEach(c => {
                    c.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                });
                document.querySelectorAll('.suggestion-card').forEach(c => {
                    c.classList.remove('ring-2', 'ring-blue-500');
                });

                // Redirect after delay
                setTimeout(() => {
                    window.location.href = data.redirect || '/login';
                }, 3000);
            } else {
                // Show modern error message
                alertContainer.innerHTML = `
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                            <div>
                                <h4 class="text-red-800 dark:text-red-200 font-semibold">Booking Failed</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mt-1">${data.message}</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            // Hide loading state
            spinner.classList.add('hidden');
            buttonText.classList.remove('hidden');

            if (error.errors) {
                // Display validation errors
                let errorList = `
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl p-4">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-red-800 dark:text-red-200 font-semibold">Please fix the following errors:</h4>
                                <ul class="text-red-700 dark:text-red-300 text-sm mt-2 space-y-1">
                `;
                for (const [field, messages] of Object.entries(error.errors)) {
                    messages.forEach(message => {
                        errorList += `<li>• ${message}</li>`;
                    });
                }
                errorList += `
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
                alertContainer.innerHTML = errorList;
            } else {
                // Show generic error
                alertContainer.innerHTML = `
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                            <div>
                                <h4 class="text-red-800 dark:text-red-200 font-semibold">Something went wrong</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mt-1">Please try again later or contact support.</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        });
    });
});
    </script>
</body>

</html>