﻿@include('layouts.header')

<body class="bg-medical-bg-light">
@include('layouts.navbar')

<!-- Modern Hero Section -->
<section class="bg-gradient-to-br from-medical-bg-light to-medical-border-light py-16 md:py-24">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center">
            <!-- Left Side: Content -->
            <div class="lg:w-1/2 mb-10 lg:mb-0 animate-fade-in">
                <span class="bg-medical-accent/10 text-medical-accent px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">
                    Virtual Healthcare
                </span>
                <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-medical-neutral-600 leading-tight">
                    Healthcare That <span class="text-medical-primary">Works</span> Around Your Life
                </h1>
                <p class="text-lg text-medical-neutral-500 mb-8 max-w-lg">
                    Connect with qualified doctors for personalized care through secure consultations — accessible from anywhere, whenever you need it.
                </p>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 mb-8">
                    <a href="{{route('appointment')}}" class="inline-flex items-center justify-center bg-medical-primary hover:bg-medical-primary/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105">
                        Get Started
                        <i data-lucide="arrow-right" class="ml-2 h-4 w-4"></i>
                    </a>
                    <a href="#services" class="inline-flex items-center justify-center border border-medical-primary text-medical-primary hover:bg-medical-primary/10 px-6 py-3 rounded-lg font-medium transition-all duration-200">
                        Learn More
                    </a>
                </div>

                <!-- Trust Indicators -->
                <div class="mb-8">
                    <div class="flex items-center mb-3">
                        <i data-lucide="shield-check" class="text-medical-primary h-5 w-5 mr-2"></i>
                        <span class="text-medical-neutral-600 font-medium">HIPAA-compliant, secure, and private</span>
                    </div>

                    <!-- Feature Grid -->
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6">
                        <div class="flex items-center">
                            <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                                <i data-lucide="video" class="text-medical-secondary h-5 w-5"></i>
                            </div>
                            <div>
                                <span class="text-medical-neutral-600 font-medium">Video Consults</span>
                                <p class="text-xs text-medical-neutral-500">Face-to-face care</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                                <i data-lucide="headphones" class="text-medical-secondary h-5 w-5"></i>
                            </div>
                            <div>
                                <span class="text-medical-neutral-600 font-medium">Audio Sessions</span>
                                <p class="text-xs text-medical-neutral-500">Quick follow-ups</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                                <i data-lucide="calendar" class="text-medical-secondary h-5 w-5"></i>
                            </div>
                            <div>
                                <span class="text-medical-neutral-600 font-medium">Easy Booking</span>
                                <p class="text-xs text-medical-neutral-500">On your schedule</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side: Image -->
            <div class="lg:w-1/2 lg:pl-10 animate-slide-up">
                <div class="relative rounded-2xl overflow-hidden shadow-xl">
                    <img
                        src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Doctor on video call with patient"
                        class="w-full h-auto rounded-xl"
                    />
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-white text-lg font-medium mr-2">Professional Care</span>
                                <span class="bg-green-500 rounded-full h-2 w-2 animate-pulse"></span>
                            </div>
                            <span class="text-white bg-black/30 px-3 py-1 rounded-full text-xs">Available Now</span>
                        </div>
                    </div>

                    <!-- Testimonial overlay -->
                    <div class="absolute top-4 right-4 bg-white rounded-lg p-3 max-w-[180px] shadow-lg">
                        <div class="flex mb-2">
                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                        </div>
                        <p class="text-xs text-medical-neutral-600 italic">
                            "The online appointment system has been life-changing for my healthcare management."
                        </p>
                        <p class="text-xs font-semibold mt-1 text-medical-primary">— Sarah T.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-12 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div class="text-center animate-fade-in">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-3xl font-bold text-medical-primary mb-2">355k+</h3>
                    <p class="text-medical-neutral-500">Happy Patients</p>
                </div>
            </div>
            <div class="text-center animate-fade-in" style="animation-delay: 0.1s">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-3xl font-bold text-medical-primary mb-2">98%</h3>
                    <p class="text-medical-neutral-500">Satisfaction Rate</p>
                </div>
            </div>
            <div class="text-center animate-fade-in" style="animation-delay: 0.2s">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-3xl font-bold text-medical-primary mb-2">120+</h3>
                    <p class="text-medical-neutral-500">Expert Doctors</p>
                </div>
            </div>
            <div class="text-center animate-fade-in" style="animation-delay: 0.3s">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-3xl font-bold text-medical-primary mb-2">24/7</h3>
                    <p class="text-medical-neutral-500">Available Support</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="py-16 bg-white" id="services">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center gap-12">
            <!-- Image -->
            <div class="lg:w-1/2 animate-fade-in">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                         alt="Medical professionals"
                         class="rounded-2xl shadow-xl w-full">
                    <div class="absolute -bottom-6 -right-6 bg-medical-primary text-white p-6 rounded-xl shadow-lg">
                        <div class="text-center">
                            <h3 class="text-2xl font-bold">15+</h3>
                            <p class="text-sm">Years Experience</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="lg:w-1/2 animate-slide-up">
                <span class="bg-medical-accent/10 text-medical-accent px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">
                    About Us
                </span>
                <h2 class="text-3xl md:text-4xl font-bold text-medical-neutral-600 mb-6">
                    The Great Place of <span class="text-medical-primary">Medical Care</span>
                </h2>
                <p class="text-lg text-medical-neutral-500 mb-8">
                    At our Medical Center, we are dedicated to providing exceptional healthcare services with compassion and expertise. Our state-of-the-art facilities and highly skilled medical professionals ensure that you receive the best possible care.
                </p>

                <!-- Services Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                    <div class="flex items-center p-3 bg-medical-bg-light rounded-lg">
                        <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                            <i data-lucide="ambulance" class="text-medical-secondary h-5 w-5"></i>
                        </div>
                        <span class="text-medical-neutral-600 font-medium">Emergency Services</span>
                    </div>
                    <div class="flex items-center p-3 bg-medical-bg-light rounded-lg">
                        <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                            <i data-lucide="heart-pulse" class="text-medical-secondary h-5 w-5"></i>
                        </div>
                        <span class="text-medical-neutral-600 font-medium">Specialized Care</span>
                    </div>
                    <div class="flex items-center p-3 bg-medical-bg-light rounded-lg">
                        <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                            <i data-lucide="pill" class="text-medical-secondary h-5 w-5"></i>
                        </div>
                        <span class="text-medical-neutral-600 font-medium">Pharmacy Services</span>
                    </div>
                    <div class="flex items-center p-3 bg-medical-bg-light rounded-lg">
                        <div class="h-10 w-10 rounded-full bg-medical-secondary/10 flex items-center justify-center mr-3">
                            <i data-lucide="clock" class="text-medical-secondary h-5 w-5"></i>
                        </div>
                        <span class="text-medical-neutral-600 font-medium">24/7 Support</span>
                    </div>
                </div>

                <a href="{{route('appointment')}}" class="inline-flex items-center bg-medical-primary hover:bg-medical-primary/90 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    Book Appointment
                    <i data-lucide="arrow-right" class="ml-2 h-4 w-4"></i>
                </a>
            </div>
        </div>
    </div>
</section>


    <!--============================
        SERVICE START
    ==============================-->
    <section class="service" style="background: url(assets/images/service_bg.jpg);">
    <div class="service_overlay pt_100 xs_pt_70 pb_100 xs_pb_70">
        <div class="container">
            <div class="row">
                <div class="col-xl-12">
                    <div class="common_heading center_heading mb_15">
                        <h5>Our Services</h5>
                        <h2>Comprehensive Medical Care</h2>
                    </div>
                </div>
            </div>
            <div class="row service_slider">
                <!-- Service 1: Online Monitoring -->
                <div class="col-xxl-4 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_service">
                        <div class="service_img">
                            <span class="tf_service_icon"><i class="fas fa-eye"></i></span>
                            <img src="https://www.shutterstock.com/image-photo/african-female-doctor-holding-phone-600nw-**********.jpg" alt="Online Monitoring" class="img-fluid w-100">
                        </div>
                        <div class="service_text">
                            <a  class="service_heading">Online Monitoring</a>
                            <p>Stay connected with our 24/7 online monitoring services. Track your health remotely with expert guidance from our medical team.</p>
                           
                        </div>
                    </div>
                </div>

                <!-- Service 2: Heart Surgery -->
                <div class="col-xxl-4 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_service">
                        <div class="service_img">
                            <span class="tf_service_icon tf_service_icon2"><i class="fas fa-heartbeat"></i></span>
                            <img src="assets/images/Heart_Surgery.jpeg" alt="Heart Surgery" class="img-fluid w-100">
                        </div>
                        <div class="service_text">
                            <a  class="service_heading">Heart Surgery</a>
                            <p>Advanced heart surgery procedures performed by our skilled cardiologists to ensure your heart health is in the best hands.</p>
                           
                        </div>
                    </div>
                </div>

                <!-- Service 3: Diagnosis & Research -->
                <div class="col-xxl-4 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_service">
                        <div class="service_img">
                            <span class="tf_service_icon tf_service_icon3"><i class="fad fa-capsules"></i></span>
                            <img src="https://www.shutterstock.com/shutterstock/videos/**********/thumb/1.jpg?ip=x480" alt="Diagnosis & Research" class="img-fluid w-100">
                        </div>
                        <div class="service_text">
                            <a  class="service_heading">Diagnosis & Research</a>
                            <p>Cutting-edge diagnostic tools and research-driven approaches to provide accurate and timely medical solutions.</p>
                           
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
    <!--============================
        SERVICE END
    ==============================-->





<!--============================
    PROCESS START
==============================-->
<section class="process pt_100 xs_pt_70 pb_95 xs_pb_65" style="background: url(assets/images/work_bg.jpg);">
    <div class="container process_shape">
        <div class="row">
            <div class="col-xl-12">
                <div class="common_heading center_heading mb_25">
                    <h5>How We Work</h5>
                    <h2>Our Working Process</h2>
                </div>
            </div>
        </div>
        <div class="work_process_area">
            <div class="row">
                <!-- Step 1: Fill the Form -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num1">01</span>
                        <h4>Fill the Form</h4>
                        <p>Start by filling out our simple online form with your basic details and medical history. This helps us understand your needs better.</p>
                    </div>
                </div>

                <!-- Step 2: Book an Appointment -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num2">02</span>
                        <h4>Book an Appointment</h4>
                        <p>Choose a convenient date and time for your appointment. Our team will confirm your booking and provide all necessary details.</p>
                    </div>
                </div>

                <!-- Step 3: Check-Ups -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num3">03</span>
                        <h4>Check-Ups</h4>
                        <p>Visit our facility for a thorough check-up. Our experienced doctors will assess your health and recommend the best course of action.</p>
                    </div>
                </div>

                <!-- Step 4: Get Reports -->
                <div class="col-xl-3 col-sm-6 col-lg-3 wow fadeInUp" data-wow-duration="1s">
                    <div class="single_process">
                        <span class="process_number num4">04</span>
                        <h4>Get Reports</h4>
                        <p>Receive detailed reports and personalized treatment plans. Our team will guide you through the next steps for your health journey.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--============================
    PROCESS END
==============================-->


<!-- Modern Appointment Booking Section -->
<section class="py-16 bg-gradient-to-br from-medical-primary to-medical-secondary" id="appointment">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <span class="bg-white/10 text-white px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">
                Book Appointment
            </span>
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Schedule Your Visit Today
            </h2>
            <p class="text-white/80 text-lg max-w-2xl mx-auto">
                Choose your preferred date and time for a consultation with our qualified medical professionals.
            </p>
        </div>

        <div class="max-w-6xl mx-auto">
            <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <div class="grid lg:grid-cols-2 gap-0">
                    <!-- Booking Form -->
                    <div class="p-8 lg:p-12">
                        <h3 class="text-2xl font-bold text-medical-neutral-600 mb-6">Patient Information</h3>

                        <form id="appointmentForm" class="space-y-6">
                            @csrf

                            <!-- Personal Info -->
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Patient Name *</label>
                                    <input type="text" name="patient_name" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Enter full name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Enter phone number">
                                </div>
                            </div>

                            <!-- Date & Time Selection -->
                            <div class="bg-medical-bg-light p-6 rounded-xl">
                                <h4 class="text-lg font-semibold text-medical-neutral-600 mb-4">Select Date & Time</h4>

                                <!-- Visual Calendar -->
                                <div class="grid md:grid-cols-2 gap-6">
                                    <!-- Date Picker -->
                                    <div>
                                        <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Preferred Date *</label>
                                        <input type="date" name="date" required id="appointmentDate"
                                               class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200">
                                    </div>

                                    <!-- Time Slots -->
                                    <div>
                                        <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Available Times *</label>
                                        <div class="grid grid-cols-2 gap-2" id="timeSlots">
                                            <button type="button" class="time-slot px-3 py-2 text-sm border border-medical-border-light rounded-lg hover:bg-medical-primary hover:text-white transition-all duration-200" data-time="09:00 AM">09:00 AM</button>
                                            <button type="button" class="time-slot px-3 py-2 text-sm border border-medical-border-light rounded-lg hover:bg-medical-primary hover:text-white transition-all duration-200" data-time="10:00 AM">10:00 AM</button>
                                            <button type="button" class="time-slot px-3 py-2 text-sm border border-medical-border-light rounded-lg hover:bg-medical-primary hover:text-white transition-all duration-200" data-time="11:00 AM">11:00 AM</button>
                                            <button type="button" class="time-slot px-3 py-2 text-sm border border-medical-border-light rounded-lg hover:bg-medical-primary hover:text-white transition-all duration-200" data-time="02:00 PM">02:00 PM</button>
                                            <button type="button" class="time-slot px-3 py-2 text-sm border border-medical-border-light rounded-lg hover:bg-medical-primary hover:text-white transition-all duration-200" data-time="03:00 PM">03:00 PM</button>
                                            <button type="button" class="time-slot px-3 py-2 text-sm border border-medical-border-light rounded-lg hover:bg-medical-primary hover:text-white transition-all duration-200" data-time="04:00 PM">04:00 PM</button>
                                        </div>
                                        <input type="hidden" name="time" id="selectedTime" required>
                                    </div>
                                </div>
                            </div>
                            <!-- Account Info -->
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Email Address *</label>
                                    <input type="email" name="email" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Enter email address">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Password *</label>
                                    <input type="password" name="password" required
                                           class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                           placeholder="Create password">
                                </div>
                            </div>

                            <!-- Doctor Selection -->
                            <div>
                                <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Select Doctor & Department *</label>
                                <select name="doctor" required
                                        class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200">
                                    <option value="">Choose your preferred doctor</option>
                                    @foreach($doctors as $doctor)
                                        <option value="{{ $doctor->id }}">{{ $doctor->name }} - {{ $doctor->department }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Profile Image -->
                            <div>
                                <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Profile Image</label>
                                <div class="border-2 border-dashed border-medical-border-light rounded-lg p-6 text-center hover:border-medical-primary transition-colors duration-200">
                                    <input type="file" name="image" id="imageUpload" class="hidden" accept="image/*">
                                    <label for="imageUpload" class="cursor-pointer">
                                        <i data-lucide="upload" class="h-8 w-8 text-medical-neutral-400 mx-auto mb-2"></i>
                                        <p class="text-medical-neutral-500">Click to upload profile image</p>
                                        <p class="text-xs text-medical-neutral-400 mt-1">PNG, JPG up to 5MB</p>
                                    </label>
                                </div>
                            </div>

                            <!-- Message -->
                            <div>
                                <label class="block text-sm font-medium text-medical-neutral-600 mb-2">Additional Message</label>
                                <textarea name="message" rows="4"
                                          class="w-full px-4 py-3 border border-medical-border-light rounded-lg focus:ring-2 focus:ring-medical-primary focus:border-transparent transition-all duration-200"
                                          placeholder="Tell us about your symptoms or concerns..."></textarea>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit"
                                    class="w-full bg-medical-primary hover:bg-medical-primary/90 text-white py-4 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 flex items-center justify-center">
                                <span id="spinner" class="hidden">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    Booking Appointment...
                                </span>
                                <span id="buttonText" class="flex items-center">
                                    <i data-lucide="calendar-plus" class="mr-2 h-5 w-5"></i>
                                    Book Appointment
                                </span>
                            </button>

                            <!-- Alert container -->
                            <div id="alertContainer" class="mt-4"></div>
                        </form>
                    </div>

                    <!-- Right Side: Visual Elements -->
                    <div class="bg-gradient-to-br from-medical-bg-light to-white p-8 lg:p-12 flex items-center">
                        <div class="w-full">
                            <div class="text-center mb-8">
                                <div class="w-20 h-20 bg-medical-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="stethoscope" class="h-10 w-10 text-medical-primary"></i>
                                </div>
                                <h4 class="text-xl font-semibold text-medical-neutral-600 mb-2">Professional Care</h4>
                                <p class="text-medical-neutral-500">Expert medical consultation at your convenience</p>
                            </div>

                            <!-- Features -->
                            <div class="space-y-4">
                                <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
                                    <div class="w-10 h-10 bg-medical-secondary/10 rounded-full flex items-center justify-center mr-4">
                                        <i data-lucide="clock" class="h-5 w-5 text-medical-secondary"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-medical-neutral-600">Flexible Scheduling</h5>
                                        <p class="text-sm text-medical-neutral-500">Book at your convenience</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
                                    <div class="w-10 h-10 bg-medical-secondary/10 rounded-full flex items-center justify-center mr-4">
                                        <i data-lucide="shield-check" class="h-5 w-5 text-medical-secondary"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-medical-neutral-600">Secure & Private</h5>
                                        <p class="text-sm text-medical-neutral-500">Your data is protected</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-4 bg-white rounded-lg shadow-sm">
                                    <div class="w-10 h-10 bg-medical-secondary/10 rounded-full flex items-center justify-center mr-4">
                                        <i data-lucide="user-check" class="h-5 w-5 text-medical-secondary"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-medical-neutral-600">Qualified Doctors</h5>
                                        <p class="text-sm text-medical-neutral-500">Licensed professionals</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@include('layouts.footer')

<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Time slot selection
    const timeSlots = document.querySelectorAll('.time-slot');
    const selectedTimeInput = document.getElementById('selectedTime');

    timeSlots.forEach(slot => {
        slot.addEventListener('click', function() {
            // Remove active class from all slots
            timeSlots.forEach(s => {
                s.classList.remove('bg-medical-primary', 'text-white');
                s.classList.add('border-medical-border-light');
            });

            // Add active class to selected slot
            this.classList.add('bg-medical-primary', 'text-white');
            this.classList.remove('border-medical-border-light');

            // Set the hidden input value
            selectedTimeInput.value = this.dataset.time;
        });
    });

    // Set minimum date to today
    const dateInput = document.getElementById('appointmentDate');
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
});

// Form submission
document.getElementById('appointmentForm').addEventListener('submit', function (event) {
    event.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const alertContainer = document.getElementById('alertContainer');
    const spinner = document.getElementById('spinner');
    const buttonText = document.getElementById('buttonText');

    // Validate time selection
    if (!document.getElementById('selectedTime').value) {
        alertContainer.innerHTML = `
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                Please select a time slot for your appointment.
            </div>
        `;
        return;
    }

    // Clear previous alerts
    alertContainer.innerHTML = '';

    // Show loading state
    spinner.classList.remove('hidden');
    buttonText.classList.add('hidden');

    // Submit form
    fetch("{{ route('appointment.store') }}", {
        method: "POST",
        body: formData,
        headers: {
            "X-CSRF-TOKEN": document.querySelector('input[name="_token"]').value
        }
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => {
                throw err;
            });
        }
        return response.json();
    })
    .then(data => {
        spinner.classList.add('hidden');
        buttonText.classList.remove('hidden');

        if (data.success) {
            alertContainer.innerHTML = `
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        ${data.message}
                    </div>
                </div>
            `;

            form.reset();

            // Reset time slot selection
            document.querySelectorAll('.time-slot').forEach(slot => {
                slot.classList.remove('bg-medical-primary', 'text-white');
                slot.classList.add('border-medical-border-light');
            });

            setTimeout(() => {
                window.location.href = data.redirect;
            }, 2000);
        } else {
            alertContainer.innerHTML = `
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        ${data.message}
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        spinner.classList.add('hidden');
        buttonText.classList.remove('hidden');

        if (error.errors) {
            let errorList = `
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle mr-2 mt-1"></i>
                        <div>
                            <p class="font-medium mb-2">Please fix the following errors:</p>
                            <ul class="list-disc list-inside space-y-1">
            `;
            for (const [field, messages] of Object.entries(error.errors)) {
                messages.forEach(message => {
                    errorList += `<li class="text-sm">${message}</li>`;
                });
            }
            errorList += `
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            alertContainer.innerHTML = errorList;
        } else {
            alertContainer.innerHTML = `
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        An error occurred. Please try again later.
                    </div>
                </div>
            `;
        }
    });
});
</script>
</body>

</html>