hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@rollup/rollup-linux-x64-gnu@4.45.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.0':
    '@rollup/rollup-linux-x64-musl': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@types/estree@1.0.8':
    '@types/estree': private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  asynckit@0.4.0:
    asynckit: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  delayed-stream@1.0.0:
    delayed-stream: private
  detect-libc@2.0.4:
    detect-libc: private
  dunder-proto@1.0.1:
    dunder-proto: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.3:
    form-data: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  jiti@2.4.2:
    jiti: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss@1.30.1:
    lightningcss: private
  lodash@4.17.21:
    lodash: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  require-directory@2.1.1:
    require-directory: private
  rollup@4.45.0:
    rollup: private
  rxjs@7.8.2:
    rxjs: private
  shell-quote@1.8.3:
    shell-quote: private
  source-map-js@1.2.1:
    source-map-js: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@8.1.1:
    supports-color: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tree-kill@1.2.2:
    tree-kill: private
  tslib@2.8.1:
    tslib: private
  vite-plugin-full-reload@1.2.0:
    vite-plugin-full-reload: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Sat, 12 Jul 2025 12:47:54 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@esbuild/win32-x64@0.25.6'
  - '@rollup/rollup-android-arm-eabi@4.45.0'
  - '@rollup/rollup-android-arm64@4.45.0'
  - '@rollup/rollup-darwin-arm64@4.45.0'
  - '@rollup/rollup-darwin-x64@4.45.0'
  - '@rollup/rollup-freebsd-arm64@4.45.0'
  - '@rollup/rollup-freebsd-x64@4.45.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.0'
  - '@rollup/rollup-linux-arm64-gnu@4.45.0'
  - '@rollup/rollup-linux-arm64-musl@4.45.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.0'
  - '@rollup/rollup-linux-riscv64-musl@4.45.0'
  - '@rollup/rollup-linux-s390x-gnu@4.45.0'
  - '@rollup/rollup-win32-arm64-msvc@4.45.0'
  - '@rollup/rollup-win32-ia32-msvc@4.45.0'
  - '@rollup/rollup-win32-x64-msvc@4.45.0'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.11'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /root/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
