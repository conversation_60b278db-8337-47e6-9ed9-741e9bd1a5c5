#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/root/zeofshop/medical_Appointment/node_modules/.pnpm/laravel-vite-plugin@1.3.0_vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1_/node_modules/laravel-vite-plugin/bin/node_modules:/root/zeofshop/medical_Appointment/node_modules/.pnpm/laravel-vite-plugin@1.3.0_vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1_/node_modules/laravel-vite-plugin/node_modules:/root/zeofshop/medical_Appointment/node_modules/.pnpm/laravel-vite-plugin@1.3.0_vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1_/node_modules:/root/zeofshop/medical_Appointment/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/root/zeofshop/medical_Appointment/node_modules/.pnpm/laravel-vite-plugin@1.3.0_vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1_/node_modules/laravel-vite-plugin/bin/node_modules:/root/zeofshop/medical_Appointment/node_modules/.pnpm/laravel-vite-plugin@1.3.0_vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1_/node_modules/laravel-vite-plugin/node_modules:/root/zeofshop/medical_Appointment/node_modules/.pnpm/laravel-vite-plugin@1.3.0_vite@6.3.5_jiti@2.4.2_lightningcss@1.30.1_/node_modules:/root/zeofshop/medical_Appointment/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../laravel-vite-plugin/bin/clean.js" "$@"
else
  exec node  "$basedir/../laravel-vite-plugin/bin/clean.js" "$@"
fi
